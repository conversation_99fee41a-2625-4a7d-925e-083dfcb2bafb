using UnityEngine;

[CreateAssetMenu(fileName = "New Weapon", menuName = "Weapons/Weapon Data")]
public class WeaponData : ScriptableObject
{
    [Header("Basic Info")]
    public string weaponName;
    public WeaponType weaponType;
    
    [Header("Damage")]
    public float damage = 25f;
    public float headShotMultiplier = 2f;
    public float range = 100f;
    
    [Header("Firing")]
    public float fireRate = 600f; // Rounds per minute
    public bool isAutomatic = true;
    public float recoil = 1f;
    public float spread = 0.1f;
    
    [Header("Ammo")]
    public int magazineSize = 30;
    public int maxReserveAmmo = 180;
    public float reloadTime = 2.5f;
    
    [Header("Audio")]
    public AudioClip fireSound;
    public AudioClip reloadSound;
    public AudioClip emptySound;
}

public enum WeaponType
{
    AutoRifle,
    HandCannon,
    Shotgun,
    SniperRifle,
    SMG,
    LMG
}

using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

public class ControlPoint : MonoBehaviour
{
    [Header("Control Point Settings")]
    public string pointName = "Alpha";
    public float captureTime = 5f;
    public float captureRadius = 5f;
    public int pointValue = 1; // Points per second when controlled
    
    [Header("Visual Elements")]
    public Renderer pointRenderer;
    public Material neutralMaterial;
    public Material teamAMaterial;
    public Material teamBMaterial;
    public GameObject captureUI;
    public Slider captureProgress;
    public TextMeshProUGUI pointNameText;
    public TextMeshProUGUI captureStatusText;
    
    [Header("Audio")]
    public AudioSource audioSource;
    public AudioClip captureStartSound;
    public AudioClip captureCompleteSound;
    public AudioClip contestedSound;
    
    // Private variables
    private int controllingTeam = -1; // -1 = neutral, 0 = team A, 1 = team B
    private float currentCaptureProgress = 0f;
    private int capturingTeam = -1;
    private List<PlayerHealth> playersInRange = new List<PlayerHealth>();
    private bool isContested = false;
    private GameManager gameManager;

    public enum CaptureState
    {
        Neutral,
        Capturing,
        Controlled,
        Contested
    }
    
    private CaptureState currentState = CaptureState.Neutral;

    void Start()
    {
        gameManager = FindObjectOfType<GameManager>();
        
        if (pointNameText != null)
            pointNameText.text = pointName;
            
        UpdateVisuals();
        
        // Start checking for players
        InvokeRepeating(nameof(CheckPlayersInRange), 0f, 0.1f);
    }

    void Update()
    {
        ProcessCapture();
        UpdateUI();
    }

    void CheckPlayersInRange()
    {
        playersInRange.Clear();
        
        Collider[] colliders = Physics.OverlapSphere(transform.position, captureRadius);
        
        foreach (Collider col in colliders)
        {
            PlayerHealth player = col.GetComponent<PlayerHealth>();
            if (player != null && player.IsAlive())
            {
                playersInRange.Add(player);
            }
        }
    }

    void ProcessCapture()
    {
        if (playersInRange.Count == 0)
        {
            // No players in range
            if (currentState == CaptureState.Capturing)
            {
                currentState = controllingTeam == -1 ? CaptureState.Neutral : CaptureState.Controlled;
            }
            return;
        }

        // Count players by team
        int teamACount = 0;
        int teamBCount = 0;
        
        foreach (PlayerHealth player in playersInRange)
        {
            // For now, assume player names starting with "Team" indicate team
            // In a real game, you'd have a proper team system
            if (player.name.Contains("TeamA") || player == FindObjectOfType<PlayerHealth>())
                teamACount++;
            else
                teamBCount++;
        }

        // Determine capture state
        if (teamACount > 0 && teamBCount > 0)
        {
            // Contested
            isContested = true;
            currentState = CaptureState.Contested;
            
            if (audioSource != null && contestedSound != null && !audioSource.isPlaying)
                audioSource.PlayOneShot(contestedSound);
        }
        else if (teamACount > 0)
        {
            // Team A capturing
            ProcessTeamCapture(0, teamACount);
        }
        else if (teamBCount > 0)
        {
            // Team B capturing
            ProcessTeamCapture(1, teamBCount);
        }

        UpdateVisuals();
    }

    void ProcessTeamCapture(int team, int playerCount)
    {
        isContested = false;
        
        if (controllingTeam == team)
        {
            // Already controlled by this team
            currentState = CaptureState.Controlled;
            return;
        }

        // Start or continue capture
        if (capturingTeam != team)
        {
            // New team started capturing
            capturingTeam = team;
            currentCaptureProgress = 0f;
            currentState = CaptureState.Capturing;
            
            if (audioSource != null && captureStartSound != null)
                audioSource.PlayOneShot(captureStartSound);
        }

        // Progress capture (more players = faster capture)
        float captureRate = playerCount * (1f / captureTime);
        currentCaptureProgress += captureRate * Time.deltaTime;

        if (currentCaptureProgress >= 1f)
        {
            // Capture complete
            CompleteCapture(team);
        }
    }

    void CompleteCapture(int team)
    {
        controllingTeam = team;
        capturingTeam = -1;
        currentCaptureProgress = 1f;
        currentState = CaptureState.Controlled;
        
        if (audioSource != null && captureCompleteSound != null)
            audioSource.PlayOneShot(captureCompleteSound);
        
        // Notify game manager
        if (gameManager != null)
        {
            gameManager.OnControlPointCaptured(this, team);
        }
        
        Debug.Log($"Control Point {pointName} captured by Team {(team == 0 ? "A" : "B")}!");
    }

    void UpdateVisuals()
    {
        if (pointRenderer == null) return;

        switch (currentState)
        {
            case CaptureState.Neutral:
                pointRenderer.material = neutralMaterial;
                break;
            case CaptureState.Capturing:
                // Blend between neutral and capturing team color
                if (capturingTeam == 0)
                    pointRenderer.material = teamAMaterial;
                else
                    pointRenderer.material = teamBMaterial;
                break;
            case CaptureState.Controlled:
                if (controllingTeam == 0)
                    pointRenderer.material = teamAMaterial;
                else
                    pointRenderer.material = teamBMaterial;
                break;
            case CaptureState.Contested:
                // Flash between team colors or use special contested material
                pointRenderer.material = neutralMaterial;
                break;
        }
    }

    void UpdateUI()
    {
        if (captureUI == null) return;

        // Show UI only when players are nearby
        bool shouldShowUI = playersInRange.Count > 0;
        captureUI.SetActive(shouldShowUI);

        if (!shouldShowUI) return;

        // Update capture progress bar
        if (captureProgress != null)
        {
            captureProgress.value = currentCaptureProgress;
        }

        // Update status text
        if (captureStatusText != null)
        {
            switch (currentState)
            {
                case CaptureState.Neutral:
                    captureStatusText.text = "Neutral";
                    break;
                case CaptureState.Capturing:
                    string teamName = capturingTeam == 0 ? "Team A" : "Team B";
                    captureStatusText.text = $"{teamName} Capturing...";
                    break;
                case CaptureState.Controlled:
                    string controllerName = controllingTeam == 0 ? "Team A" : "Team B";
                    captureStatusText.text = $"Controlled by {controllerName}";
                    break;
                case CaptureState.Contested:
                    captureStatusText.text = "CONTESTED";
                    break;
            }
        }
    }

    public bool IsControlledBy(int team)
    {
        return controllingTeam == team && currentState == CaptureState.Controlled;
    }

    public int GetControllingTeam()
    {
        return controllingTeam;
    }

    public float GetCaptureProgress()
    {
        return currentCaptureProgress;
    }

    public CaptureState GetCaptureState()
    {
        return currentState;
    }

    void OnDrawGizmosSelected()
    {
        // Draw capture radius
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, captureRadius);
        
        // Draw point name
        Gizmos.color = Color.white;
        #if UNITY_EDITOR
        UnityEditor.Handles.Label(transform.position + Vector3.up * 2f, pointName);
        #endif
    }
}

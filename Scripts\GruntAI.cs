using UnityEngine;
using UnityEngine.AI;

public class GruntAI : MonoBehaviour
{
    [Header("AI Stats")]
    public float health = 50f;
    public float damage = 15f;
    public float moveSpeed = 3f;
    public float attackRange = 15f;
    public float detectionRange = 25f;
    public float fireRate = 2f;
    
    [Header("Behavior")]
    public float patrolRadius = 10f;
    public float chaseTime = 5f;
    public float alertTime = 3f;
    
    private NavMeshAgent agent;
    private Transform player;
    private Transform firePoint;
    private float lastFireTime;
    private float lastSeePlayerTime;
    private Vector3 startPosition;
    private Vector3 patrolTarget;
    
    public enum AIState
    {
        Patrol,
        Chase,
        Attack,
        Alert,
        Dead
    }
    
    public AIState currentState = AIState.Patrol;
    
    void Start()
    {
        agent = GetComponent<NavMeshAgent>();
        agent.speed = moveSpeed;
        
        player = GameObject.FindGameObjectWithTag("Player")?.transform;
        firePoint = transform.Find("Fire Point");
        
        startPosition = transform.position;
        SetNewPatrolTarget();
    }
    
    void Update()
    {
        if (health <= 0)
        {
            currentState = AIState.Dead;
            return;
        }
        
        switch (currentState)
        {
            case AIState.Patrol:
                PatrolBehavior();
                break;
            case AIState.Chase:
                ChaseBehavior();
                break;
            case AIState.Attack:
                AttackBehavior();
                break;
            case AIState.Alert:
                AlertBehavior();
                break;
            case AIState.Dead:
                DeadBehavior();
                break;
        }
        
        CheckForPlayer();
    }
    
    void PatrolBehavior()
    {
        if (!agent.pathPending && agent.remainingDistance < 0.5f)
        {
            SetNewPatrolTarget();
        }
        
        agent.SetDestination(patrolTarget);
    }
    
    void ChaseBehavior()
    {
        if (player != null)
        {
            float distanceToPlayer = Vector3.Distance(transform.position, player.position);
            
            if (distanceToPlayer <= attackRange)
            {
                currentState = AIState.Attack;
            }
            else if (distanceToPlayer > detectionRange && Time.time - lastSeePlayerTime > chaseTime)
            {
                currentState = AIState.Alert;
            }
            else
            {
                agent.SetDestination(player.position);
            }
        }
    }
    
    void AttackBehavior()
    {
        if (player != null)
        {
            float distanceToPlayer = Vector3.Distance(transform.position, player.position);
            
            if (distanceToPlayer > attackRange)
            {
                currentState = AIState.Chase;
                return;
            }
            
            // Stop moving and face player
            agent.SetDestination(transform.position);
            Vector3 lookDirection = (player.position - transform.position).normalized;
            lookDirection.y = 0;
            transform.rotation = Quaternion.LookRotation(lookDirection);
            
            // Fire at player
            if (Time.time - lastFireTime > 1f / fireRate)
            {
                FireAtPlayer();
                lastFireTime = Time.time;
            }
        }
    }
    
    void AlertBehavior()
    {
        // Look around for player
        agent.SetDestination(transform.position);
        
        if (Time.time - lastSeePlayerTime > alertTime)
        {
            currentState = AIState.Patrol;
            SetNewPatrolTarget();
        }
    }
    
    void DeadBehavior()
    {
        agent.enabled = false;
        GetComponent<Collider>().enabled = false;
        
        // Play death animation or effects here
        Destroy(gameObject, 3f);
    }
    
    void CheckForPlayer()
    {
        if (player == null) return;
        
        float distanceToPlayer = Vector3.Distance(transform.position, player.position);
        
        if (distanceToPlayer <= detectionRange)
        {
            // Check if player is visible
            Vector3 directionToPlayer = (player.position - transform.position).normalized;
            RaycastHit hit;
            
            if (Physics.Raycast(transform.position + Vector3.up, directionToPlayer, out hit, detectionRange))
            {
                if (hit.collider.CompareTag("Player"))
                {
                    lastSeePlayerTime = Time.time;
                    
                    if (currentState == AIState.Patrol || currentState == AIState.Alert)
                    {
                        currentState = distanceToPlayer <= attackRange ? AIState.Attack : AIState.Chase;
                    }
                }
            }
        }
    }
    
    void SetNewPatrolTarget()
    {
        Vector3 randomDirection = Random.insideUnitSphere * patrolRadius;
        randomDirection += startPosition;
        
        NavMeshHit hit;
        if (NavMesh.SamplePosition(randomDirection, out hit, patrolRadius, 1))
        {
            patrolTarget = hit.position;
        }
        else
        {
            patrolTarget = startPosition;
        }
    }
    
    void FireAtPlayer()
    {
        if (player != null && firePoint != null)
        {
            Vector3 fireDirection = (player.position - firePoint.position).normalized;
            
            // Create bullet or projectile
            RaycastHit hit;
            if (Physics.Raycast(firePoint.position, fireDirection, out hit, attackRange))
            {
                if (hit.collider.CompareTag("Player"))
                {
                    PlayerHealth playerHealth = hit.collider.GetComponent<PlayerHealth>();
                    if (playerHealth != null)
                    {
                        playerHealth.TakeDamage(damage);
                    }
                }
            }
            
            // Visual effects
            CreateMuzzleFlash();
            CreateBulletTrail(firePoint.position, firePoint.position + fireDirection * attackRange);
        }
    }
    
    void CreateMuzzleFlash()
    {
        // Create muzzle flash effect at fire point
        if (firePoint != null)
        {
            GameObject flash = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            flash.transform.position = firePoint.position;
            flash.transform.localScale = Vector3.one * 0.2f;
            flash.GetComponent<Renderer>().material.color = Color.yellow;
            Destroy(flash, 0.1f);
        }
    }
    
    void CreateBulletTrail(Vector3 start, Vector3 end)
    {
        // Create bullet trail effect
        LineRenderer trail = new GameObject("Bullet Trail").AddComponent<LineRenderer>();
        trail.material = new Material(Shader.Find("Sprites/Default"));
        trail.color = Color.yellow;
        trail.startWidth = 0.02f;
        trail.endWidth = 0.02f;
        trail.positionCount = 2;
        trail.SetPosition(0, start);
        trail.SetPosition(1, end);
        
        Destroy(trail.gameObject, 0.1f);
    }
    
    public void TakeDamage(float damageAmount)
    {
        health -= damageAmount;
        
        if (health <= 0)
        {
            currentState = AIState.Dead;
            
            // Award points to player
            AdvancedGameManager gameManager = FindObjectOfType<AdvancedGameManager>();
            if (gameManager != null)
            {
                gameManager.AddKill();
            }
        }
        else
        {
            // React to damage
            if (currentState == AIState.Patrol)
            {
                currentState = AIState.Alert;
                lastSeePlayerTime = Time.time;
            }
        }
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw detection range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRange);
        
        // Draw attack range
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);
        
        // Draw patrol radius
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(startPosition, patrolRadius);
    }
}

using UnityEngine;

public class ModelImportHelper : MonoBehaviour
{
    [<PERSON><PERSON>("Quick Model Setup")]
    [Toolt<PERSON>("Drag your downloaded 3D model here")]
    public GameObject modelToSetup;
    
    [Header("Weapon Configuration")]
    public WeaponType weaponType = WeaponType.AutoRifle;
    public string weaponName = "New Weapon";
    
    [Head<PERSON>("Fire Point Setup")]
    [Tooltip("Where bullets should come from (usually barrel end)")]
    public Transform firePointReference;
    
    [ContextMenu("Setup Weapon Model")]
    public void SetupWeaponModel()
    {
        if (modelToSetup == null)
        {
            Debug.LogError("No model assigned! Drag a 3D model to 'Model To Setup' field.");
            return;
        }
        
        // Create weapon prefab
        GameObject weaponPrefab = new GameObject(weaponName + "_Weapon");
        
        // Instantiate the model as child
        GameObject modelInstance = Instantiate(modelToSetup, weaponPrefab.transform);
        modelInstance.name = "Model";
        
        // Add weapon controller
        WeaponController weaponController = weaponPrefab.AddComponent<WeaponController>();
        
        // Setup fire point
        GameObject firePoint = new GameObject("Fire Point");
        firePoint.transform.parent = modelInstance.transform;
        
        if (firePointReference != null)
        {
            firePoint.transform.position = firePointReference.position;
        }
        else
        {
            // Auto-position fire point at front of model
            Renderer renderer = modelInstance.GetComponentInChildren<Renderer>();
            if (renderer != null)
            {
                Bounds bounds = renderer.bounds;
                firePoint.transform.position = new Vector3(bounds.center.x, bounds.center.y, bounds.max.z);
            }
        }
        
        weaponController.firePoint = firePoint.transform;
        
        // Create weapon data asset
        CreateWeaponDataAsset();
        
        // Save as prefab
        string prefabPath = $"Assets/Prefabs/Weapons/{weaponName}_Weapon.prefab";
        
        #if UNITY_EDITOR
        // Create directories if they don't exist
        if (!UnityEditor.AssetDatabase.IsValidFolder("Assets/Prefabs"))
            UnityEditor.AssetDatabase.CreateFolder("Assets", "Prefabs");
        if (!UnityEditor.AssetDatabase.IsValidFolder("Assets/Prefabs/Weapons"))
            UnityEditor.AssetDatabase.CreateFolder("Assets/Prefabs", "Weapons");
            
        UnityEditor.PrefabUtility.SaveAsPrefabAsset(weaponPrefab, prefabPath);
        #endif
        
        Debug.Log($"Weapon prefab created: {prefabPath}");
        
        // Clean up temporary object
        DestroyImmediate(weaponPrefab);
    }
    
    void CreateWeaponDataAsset()
    {
        #if UNITY_EDITOR
        WeaponData weaponData = ScriptableObject.CreateInstance<WeaponData>();
        weaponData.weaponName = weaponName;
        weaponData.weaponType = weaponType;
        
        // Set default stats based on weapon type
        switch (weaponType)
        {
            case WeaponType.AutoRifle:
                weaponData.damage = 25f;
                weaponData.fireRate = 600f;
                weaponData.magazineSize = 30;
                weaponData.reloadTime = 2.5f;
                weaponData.range = 100f;
                weaponData.isAutomatic = true;
                break;
                
            case WeaponType.HandCannon:
                weaponData.damage = 70f;
                weaponData.fireRate = 180f;
                weaponData.magazineSize = 12;
                weaponData.reloadTime = 2.8f;
                weaponData.range = 80f;
                weaponData.isAutomatic = false;
                break;
                
            case WeaponType.Shotgun:
                weaponData.damage = 15f; // Per pellet
                weaponData.fireRate = 120f;
                weaponData.magazineSize = 8;
                weaponData.reloadTime = 3.5f;
                weaponData.range = 25f;
                weaponData.isAutomatic = false;
                weaponData.spread = 0.3f;
                break;
                
            case WeaponType.SniperRifle:
                weaponData.damage = 150f;
                weaponData.fireRate = 60f;
                weaponData.magazineSize = 5;
                weaponData.reloadTime = 3.0f;
                weaponData.range = 200f;
                weaponData.isAutomatic = false;
                weaponData.headShotMultiplier = 3f;
                break;
                
            case WeaponType.SMG:
                weaponData.damage = 18f;
                weaponData.fireRate = 900f;
                weaponData.magazineSize = 40;
                weaponData.reloadTime = 2.0f;
                weaponData.range = 60f;
                weaponData.isAutomatic = true;
                break;
        }
        
        // Create directories if they don't exist
        if (!UnityEditor.AssetDatabase.IsValidFolder("Assets/ScriptableObjects"))
            UnityEditor.AssetDatabase.CreateFolder("Assets", "ScriptableObjects");
        if (!UnityEditor.AssetDatabase.IsValidFolder("Assets/ScriptableObjects/Weapons"))
            UnityEditor.AssetDatabase.CreateFolder("Assets/ScriptableObjects", "Weapons");
        
        string assetPath = $"Assets/ScriptableObjects/Weapons/{weaponName}_Data.asset";
        UnityEditor.AssetDatabase.CreateAsset(weaponData, assetPath);
        UnityEditor.AssetDatabase.SaveAssets();
        
        Debug.Log($"Weapon data created: {assetPath}");
        #endif
    }
    
    [ContextMenu("Auto-Find Fire Point")]
    public void AutoFindFirePoint()
    {
        if (modelToSetup == null) return;
        
        // Look for common fire point names
        string[] firePointNames = { "FirePoint", "Fire_Point", "Muzzle", "Barrel_End", "Gun_Tip" };
        
        foreach (string name in firePointNames)
        {
            Transform found = FindChildRecursive(modelToSetup.transform, name);
            if (found != null)
            {
                firePointReference = found;
                Debug.Log($"Found fire point: {found.name}");
                return;
            }
        }
        
        Debug.Log("No fire point found automatically. Please assign manually or use auto-positioning.");
    }
    
    Transform FindChildRecursive(Transform parent, string name)
    {
        foreach (Transform child in parent)
        {
            if (child.name.Contains(name))
                return child;
                
            Transform found = FindChildRecursive(child, name);
            if (found != null)
                return found;
        }
        return null;
    }
}

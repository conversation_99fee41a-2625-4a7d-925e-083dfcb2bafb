using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using TMPro;

public class MainMenu : MonoBehaviour
{
    [Header("Menu Panels")]
    public GameObject mainMenuPanel;
    public GameObject playMenuPanel;
    public GameObject settingsPanel;
    public GameObject creditsPanel;
    public GameObject loadingPanel;

    [Header("Play Menu")]
    public Button quickPlayButton;
    public Button customGameButton;
    public TMP_Dropdown gameModeDropdown;
    public TMP_Dropdown mapDropdown;
    public Slider maxPlayersSlider;
    public TextMeshProUGUI maxPlayersText;

    [Header("Settings")]
    public Slider mouseSensitivitySlider;
    public Slider masterVolumeSlider;
    public Slider musicVolumeSlider;
    public Slider sfxVolumeSlider;
    public Toggle fullscreenToggle;
    public TMP_Dropdown qualityDropdown;
    public TMP_Dropdown resolutionDropdown;
    public Toggle vsyncToggle;

    [Header("Audio")]
    public AudioSource buttonClickSound;
    public AudioSource backgroundMusic;

    [Header("Loading")]
    public Slider loadingBar;
    public TextMeshProUGUI loadingText;
    public TextMeshProUGUI tipText;

    private string[] loadingTips = {
        "Tip: Use cover to regenerate your shield!",
        "Tip: Different weapons excel at different ranges.",
        "Tip: Sliding can help you escape dangerous situations.",
        "Tip: Watch your radar for enemy positions.",
        "Tip: Headshots deal extra damage with most weapons."
    };

    void Start()
    {
        // Unlock cursor for menu
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;

        // Load saved settings
        LoadSettings();

        // Initialize dropdowns
        InitializeDropdowns();

        // Show main menu by default
        ShowMainMenu();

        // Start background music
        if (backgroundMusic != null && !backgroundMusic.isPlaying)
            backgroundMusic.Play();
    }

    void InitializeDropdowns()
    {
        // Initialize game mode dropdown
        if (gameModeDropdown != null)
        {
            gameModeDropdown.ClearOptions();
            gameModeDropdown.AddOptions(new System.Collections.Generic.List<string>
            {
                "Team Deathmatch", "Free For All", "Control", "Elimination"
            });
        }

        // Initialize map dropdown
        if (mapDropdown != null)
        {
            mapDropdown.ClearOptions();
            mapDropdown.AddOptions(new System.Collections.Generic.List<string>
            {
                "Arena Alpha", "Desert Outpost", "Space Station", "Urban Ruins"
            });
        }

        // Initialize resolution dropdown
        if (resolutionDropdown != null)
        {
            resolutionDropdown.ClearOptions();
            var resolutions = new System.Collections.Generic.List<string>();
            foreach (var res in Screen.resolutions)
            {
                resolutions.Add($"{res.width} x {res.height}");
            }
            resolutionDropdown.AddOptions(resolutions);
        }

        // Set max players slider
        if (maxPlayersSlider != null)
        {
            maxPlayersSlider.onValueChanged.AddListener(OnMaxPlayersChanged);
            OnMaxPlayersChanged(maxPlayersSlider.value);
        }
    }

    public void ShowPlayMenu()
    {
        PlayButtonSound();
        mainMenuPanel.SetActive(false);
        playMenuPanel.SetActive(true);
        settingsPanel.SetActive(false);
        creditsPanel.SetActive(false);
        loadingPanel.SetActive(false);
    }

    public void QuickPlay()
    {
        PlayButtonSound();
        StartGame("GameScene");
    }

    public void CustomGame()
    {
        PlayButtonSound();
        // Get selected options
        string gameMode = gameModeDropdown.options[gameModeDropdown.value].text;
        string map = mapDropdown.options[mapDropdown.value].text;
        int maxPlayers = Mathf.RoundToInt(maxPlayersSlider.value);

        // Save game settings
        PlayerPrefs.SetString("GameMode", gameMode);
        PlayerPrefs.SetString("Map", map);
        PlayerPrefs.SetInt("MaxPlayers", maxPlayers);

        StartGame("GameScene");
    }

    void StartGame(string sceneName)
    {
        // Show loading screen
        ShowLoadingScreen();

        // Start loading scene
        StartCoroutine(LoadSceneAsync(sceneName));
    }

    void ShowLoadingScreen()
    {
        mainMenuPanel.SetActive(false);
        playMenuPanel.SetActive(false);
        settingsPanel.SetActive(false);
        creditsPanel.SetActive(false);
        loadingPanel.SetActive(true);

        // Show random tip
        if (tipText != null)
        {
            tipText.text = loadingTips[Random.Range(0, loadingTips.Length)];
        }
    }

    System.Collections.IEnumerator LoadSceneAsync(string sceneName)
    {
        AsyncOperation operation = SceneManager.LoadSceneAsync(sceneName);
        operation.allowSceneActivation = false;

        while (!operation.isDone)
        {
            float progress = Mathf.Clamp01(operation.progress / 0.9f);

            if (loadingBar != null)
                loadingBar.value = progress;

            if (loadingText != null)
                loadingText.text = $"Loading... {Mathf.RoundToInt(progress * 100)}%";

            if (operation.progress >= 0.9f)
            {
                if (loadingText != null)
                    loadingText.text = "Press any key to continue...";

                if (Input.anyKeyDown)
                {
                    operation.allowSceneActivation = true;
                }
            }

            yield return null;
        }
    }

    public void ShowSettings()
    {
        PlayButtonSound();
        mainMenuPanel.SetActive(false);
        playMenuPanel.SetActive(false);
        settingsPanel.SetActive(true);
        creditsPanel.SetActive(false);
        loadingPanel.SetActive(false);
    }

    public void ShowCredits()
    {
        PlayButtonSound();
        mainMenuPanel.SetActive(false);
        playMenuPanel.SetActive(false);
        settingsPanel.SetActive(false);
        creditsPanel.SetActive(true);
        loadingPanel.SetActive(false);
    }

    public void ShowMainMenu()
    {
        PlayButtonSound();
        mainMenuPanel.SetActive(true);
        playMenuPanel.SetActive(false);
        settingsPanel.SetActive(false);
        creditsPanel.SetActive(false);
        loadingPanel.SetActive(false);
    }

    public void QuitGame()
    {
        PlayButtonSound();
        Debug.Log("Quitting game...");
        Application.Quit();
    }

    public void OnMouseSensitivityChanged()
    {
        float sensitivity = mouseSensitivitySlider.value;
        PlayerPrefs.SetFloat("MouseSensitivity", sensitivity);
        
        // Apply to any existing player controller
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            player.mouseSensitivity = sensitivity;
        }
    }

    public void OnMasterVolumeChanged()
    {
        float volume = masterVolumeSlider.value;
        PlayerPrefs.SetFloat("MasterVolume", volume);
        AudioListener.volume = volume;
    }

    public void OnFullscreenToggled()
    {
        bool isFullscreen = fullscreenToggle.isOn;
        PlayerPrefs.SetInt("Fullscreen", isFullscreen ? 1 : 0);
        Screen.fullScreen = isFullscreen;
    }

    public void OnQualityChanged()
    {
        int qualityIndex = qualityDropdown.value;
        PlayerPrefs.SetInt("QualityLevel", qualityIndex);
        QualitySettings.SetQualityLevel(qualityIndex);
    }

    void LoadSettings()
    {
        // Mouse sensitivity
        float mouseSensitivity = PlayerPrefs.GetFloat("MouseSensitivity", 2f);
        if (mouseSensitivitySlider != null)
        {
            mouseSensitivitySlider.value = mouseSensitivity;
        }

        // Master volume
        float masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f);
        if (masterVolumeSlider != null)
        {
            masterVolumeSlider.value = masterVolume;
        }
        AudioListener.volume = masterVolume;

        // Fullscreen
        bool isFullscreen = PlayerPrefs.GetInt("Fullscreen", 1) == 1;
        if (fullscreenToggle != null)
        {
            fullscreenToggle.isOn = isFullscreen;
        }
        Screen.fullScreen = isFullscreen;

        // Quality
        int qualityLevel = PlayerPrefs.GetInt("QualityLevel", QualitySettings.GetQualityLevel());
        if (qualityDropdown != null)
        {
            qualityDropdown.value = qualityLevel;
        }
        QualitySettings.SetQualityLevel(qualityLevel);
    }

    void PlayButtonSound()
    {
        if (buttonClickSound != null)
        {
            buttonClickSound.Play();
        }
    }
}

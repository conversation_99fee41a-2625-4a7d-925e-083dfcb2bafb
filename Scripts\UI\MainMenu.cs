using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

public class MainMenu : MonoBehaviour
{
    [<PERSON>er("Menu Panels")]
    public GameObject mainMenuPanel;
    public GameObject settingsPanel;
    public GameObject creditsPanel;
    
    [Header("Settings")]
    public Slider mouseSensitivitySlider;
    public Slider masterVolumeSlider;
    public Toggle fullscreenToggle;
    public Dropdown qualityDropdown;
    
    [Header("Audio")]
    public AudioSource buttonClickSound;

    void Start()
    {
        // Unlock cursor for menu
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
        
        // Load saved settings
        LoadSettings();
        
        // Show main menu by default
        ShowMainMenu();
    }

    public void PlayGame()
    {
        PlayButtonSound();
        // Load the main game scene
        SceneManager.LoadScene("GameScene");
    }

    public void ShowSettings()
    {
        PlayButtonSound();
        mainMenuPanel.SetActive(false);
        settingsPanel.SetActive(true);
        creditsPanel.SetActive(false);
    }

    public void ShowCredits()
    {
        PlayButtonSound();
        mainMenuPanel.SetActive(false);
        settingsPanel.SetActive(false);
        creditsPanel.SetActive(true);
    }

    public void ShowMainMenu()
    {
        PlayButtonSound();
        mainMenuPanel.SetActive(true);
        settingsPanel.SetActive(false);
        creditsPanel.SetActive(false);
    }

    public void QuitGame()
    {
        PlayButtonSound();
        Debug.Log("Quitting game...");
        Application.Quit();
    }

    public void OnMouseSensitivityChanged()
    {
        float sensitivity = mouseSensitivitySlider.value;
        PlayerPrefs.SetFloat("MouseSensitivity", sensitivity);
        
        // Apply to any existing player controller
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            player.mouseSensitivity = sensitivity;
        }
    }

    public void OnMasterVolumeChanged()
    {
        float volume = masterVolumeSlider.value;
        PlayerPrefs.SetFloat("MasterVolume", volume);
        AudioListener.volume = volume;
    }

    public void OnFullscreenToggled()
    {
        bool isFullscreen = fullscreenToggle.isOn;
        PlayerPrefs.SetInt("Fullscreen", isFullscreen ? 1 : 0);
        Screen.fullScreen = isFullscreen;
    }

    public void OnQualityChanged()
    {
        int qualityIndex = qualityDropdown.value;
        PlayerPrefs.SetInt("QualityLevel", qualityIndex);
        QualitySettings.SetQualityLevel(qualityIndex);
    }

    void LoadSettings()
    {
        // Mouse sensitivity
        float mouseSensitivity = PlayerPrefs.GetFloat("MouseSensitivity", 2f);
        if (mouseSensitivitySlider != null)
        {
            mouseSensitivitySlider.value = mouseSensitivity;
        }

        // Master volume
        float masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f);
        if (masterVolumeSlider != null)
        {
            masterVolumeSlider.value = masterVolume;
        }
        AudioListener.volume = masterVolume;

        // Fullscreen
        bool isFullscreen = PlayerPrefs.GetInt("Fullscreen", 1) == 1;
        if (fullscreenToggle != null)
        {
            fullscreenToggle.isOn = isFullscreen;
        }
        Screen.fullScreen = isFullscreen;

        // Quality
        int qualityLevel = PlayerPrefs.GetInt("QualityLevel", QualitySettings.GetQualityLevel());
        if (qualityDropdown != null)
        {
            qualityDropdown.value = qualityLevel;
        }
        QualitySettings.SetQualityLevel(qualityLevel);
    }

    void PlayButtonSound()
    {
        if (buttonClickSound != null)
        {
            buttonClickSound.Play();
        }
    }
}

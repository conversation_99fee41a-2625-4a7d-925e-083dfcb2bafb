using UnityEngine;
using UnityEngine.AI;

public class SimpleAI : MonoBehaviour
{
    [Header("AI Settings")]
    public float detectionRange = 15f;
    public float attackRange = 10f;
    public float fireRate = 2f;
    public float damage = 20f;
    public LayerMask playerLayer;
    
    [Header("Movement")]
    public float patrolRadius = 10f;
    public float waitTime = 2f;
    
    [Header("References")]
    public Transform firePoint;
    public GameObject projectilePrefab;
    
    // Private variables
    private NavMeshAgent agent;
    private PlayerHealth targetPlayer;
    private Vector3 startPosition;
    private Vector3 patrolTarget;
    private float nextFireTime;
    private float waitTimer;
    private AIState currentState = AIState.Patrolling;
    
    public enum AIState
    {
        Patrolling,
        Chasing,
        Attacking,
        Waiting
    }

    void Start()
    {
        agent = GetComponent<NavMeshAgent>();
        startPosition = transform.position;
        SetNewPatrolTarget();
    }

    void Update()
    {
        FindNearestPlayer();
        
        switch (currentState)
        {
            case AIState.Patrolling:
                Patrol();
                break;
            case AIState.Chasing:
                ChasePlayer();
                break;
            case AIState.Attacking:
                AttackPlayer();
                break;
            case AIState.Waiting:
                Wait();
                break;
        }
    }

    void FindNearestPlayer()
    {
        Collider[] playersInRange = Physics.OverlapSphere(transform.position, detectionRange, playerLayer);
        
        float closestDistance = Mathf.Infinity;
        PlayerHealth closestPlayer = null;
        
        foreach (Collider playerCollider in playersInRange)
        {
            PlayerHealth player = playerCollider.GetComponent<PlayerHealth>();
            if (player != null && player.IsAlive())
            {
                float distance = Vector3.Distance(transform.position, player.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestPlayer = player;
                }
            }
        }
        
        targetPlayer = closestPlayer;
    }

    void Patrol()
    {
        if (targetPlayer != null)
        {
            currentState = AIState.Chasing;
            return;
        }
        
        if (!agent.pathPending && agent.remainingDistance < 0.5f)
        {
            currentState = AIState.Waiting;
            waitTimer = waitTime;
        }
    }

    void ChasePlayer()
    {
        if (targetPlayer == null || !targetPlayer.IsAlive())
        {
            currentState = AIState.Patrolling;
            SetNewPatrolTarget();
            return;
        }
        
        float distanceToPlayer = Vector3.Distance(transform.position, targetPlayer.transform.position);
        
        if (distanceToPlayer > detectionRange)
        {
            currentState = AIState.Patrolling;
            SetNewPatrolTarget();
            return;
        }
        
        if (distanceToPlayer <= attackRange)
        {
            currentState = AIState.Attacking;
            agent.SetDestination(transform.position); // Stop moving
            return;
        }
        
        agent.SetDestination(targetPlayer.transform.position);
    }

    void AttackPlayer()
    {
        if (targetPlayer == null || !targetPlayer.IsAlive())
        {
            currentState = AIState.Patrolling;
            SetNewPatrolTarget();
            return;
        }
        
        float distanceToPlayer = Vector3.Distance(transform.position, targetPlayer.transform.position);
        
        if (distanceToPlayer > attackRange)
        {
            currentState = AIState.Chasing;
            return;
        }
        
        // Look at player
        Vector3 lookDirection = (targetPlayer.transform.position - transform.position).normalized;
        lookDirection.y = 0; // Keep AI upright
        transform.rotation = Quaternion.LookRotation(lookDirection);
        
        // Shoot at player
        if (Time.time >= nextFireTime)
        {
            ShootAtPlayer();
            nextFireTime = Time.time + 1f / fireRate;
        }
    }

    void Wait()
    {
        if (targetPlayer != null)
        {
            currentState = AIState.Chasing;
            return;
        }
        
        waitTimer -= Time.deltaTime;
        if (waitTimer <= 0)
        {
            SetNewPatrolTarget();
            currentState = AIState.Patrolling;
        }
    }

    void SetNewPatrolTarget()
    {
        Vector3 randomDirection = Random.insideUnitSphere * patrolRadius;
        randomDirection += startPosition;
        
        NavMeshHit hit;
        if (NavMesh.SamplePosition(randomDirection, out hit, patrolRadius, 1))
        {
            patrolTarget = hit.position;
            agent.SetDestination(patrolTarget);
        }
    }

    void ShootAtPlayer()
    {
        if (firePoint == null || targetPlayer == null) return;
        
        // Simple raycast attack
        Vector3 shootDirection = (targetPlayer.transform.position - firePoint.position).normalized;
        
        RaycastHit hit;
        if (Physics.Raycast(firePoint.position, shootDirection, out hit, attackRange))
        {
            PlayerHealth playerHealth = hit.collider.GetComponent<PlayerHealth>();
            if (playerHealth != null)
            {
                playerHealth.TakeDamage(damage);
            }
        }
        
        // Visual effect (optional)
        Debug.DrawRay(firePoint.position, shootDirection * attackRange, Color.red, 0.1f);
    }

    void OnDrawGizmosSelected()
    {
        // Draw detection range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRange);
        
        // Draw attack range
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);
        
        // Draw patrol radius
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(startPosition, patrolRadius);
    }
}

using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class HUDManager : MonoBehaviour
{
    [Header("Health & Shield")]
    public Slider healthBar;
    public Slider shieldBar;
    public TextMeshProUGUI healthText;
    public TextMeshProUGUI shieldText;
    public Image damageOverlay;
    public Image lowHealthWarning;
    
    [Header("Weapon Info")]
    public TextMeshProUGUI ammoText;
    public TextMeshProUGUI weaponNameText;
    public Image weaponIcon;
    public Slider reloadProgress;
    public GameObject reloadIndicator;
    
    [Header("Crosshair")]
    public Image crosshair;
    public Image hitMarker;
    public float hitMarkerDuration = 0.2f;
    
    [Header("Minimap")]
    public RawImage minimapCamera;
    public Transform minimapPlayer;
    public GameObject[] minimapEnemies;
    
    [Header("Score & Timer")]
    public TextMeshProUGUI scoreText;
    public TextMeshProUGUI timerText;
    public TextMeshProUGUI killFeedText;
    
    [Header("Abilities")]
    public Image abilityIcon;
    public Slider abilityCooldown;
    public TextMeshProUGUI abilityText;
    
    [Header("Notifications")]
    public GameObject killNotification;
    public TextMeshProUGUI killNotificationText;
    public GameObject objectiveNotification;
    public TextMeshProUGUI objectiveText;
    
    // Private variables
    private PlayerHealth playerHealth;
    private WeaponController currentWeapon;
    private float hitMarkerTimer;
    private bool isLowHealth;

    void Start()
    {
        playerHealth = FindObjectOfType<PlayerHealth>();
        currentWeapon = FindObjectOfType<WeaponController>();
        
        // Initialize UI elements
        if (hitMarker != null) hitMarker.color = Color.clear;
        if (reloadIndicator != null) reloadIndicator.SetActive(false);
        if (killNotification != null) killNotification.SetActive(false);
        if (objectiveNotification != null) objectiveNotification.SetActive(false);
        
        UpdateAllUI();
    }

    void Update()
    {
        UpdateHealthUI();
        UpdateWeaponUI();
        UpdateCrosshair();
        UpdateHitMarker();
        UpdateMinimap();
        CheckLowHealthWarning();
    }

    void UpdateHealthUI()
    {
        if (playerHealth == null) return;

        float healthPercent = playerHealth.GetHealthPercentage();
        float shieldPercent = playerHealth.GetShieldPercentage();

        // Update bars
        if (healthBar != null) healthBar.value = healthPercent;
        if (shieldBar != null) shieldBar.value = shieldPercent;

        // Update text
        if (healthText != null) 
            healthText.text = $"{Mathf.Ceil(healthPercent * 100)}";
        if (shieldText != null) 
            shieldText.text = $"{Mathf.Ceil(shieldPercent * 100)}";

        // Update damage overlay
        if (damageOverlay != null)
        {
            float alpha = Mathf.Lerp(0.6f, 0f, healthPercent);
            Color overlayColor = damageOverlay.color;
            overlayColor.a = alpha;
            damageOverlay.color = overlayColor;
        }
    }

    void UpdateWeaponUI()
    {
        if (currentWeapon == null) return;

        // Update ammo text
        if (ammoText != null)
        {
            // This would need to be implemented in WeaponController
            ammoText.text = "30 / 180"; // Placeholder
        }

        // Update weapon name
        if (weaponNameText != null && currentWeapon.weaponData != null)
        {
            weaponNameText.text = currentWeapon.weaponData.weaponName;
        }

        // Update reload indicator
        // This would need reload state from WeaponController
    }

    void UpdateCrosshair()
    {
        if (crosshair == null) return;

        // Dynamic crosshair based on movement/shooting
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            // Expand crosshair when moving or shooting
            float expansion = 1f; // Base size
            // Add logic for movement/shooting expansion
            
            crosshair.transform.localScale = Vector3.one * expansion;
        }
    }

    void UpdateHitMarker()
    {
        if (hitMarker == null) return;

        if (hitMarkerTimer > 0)
        {
            hitMarkerTimer -= Time.deltaTime;
            float alpha = hitMarkerTimer / hitMarkerDuration;
            Color color = hitMarker.color;
            color.a = alpha;
            hitMarker.color = color;
        }
        else
        {
            hitMarker.color = Color.clear;
        }
    }

    void UpdateMinimap()
    {
        if (minimapPlayer == null) return;

        // Rotate minimap player icon based on player rotation
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            minimapPlayer.rotation = Quaternion.Euler(0, 0, -player.transform.eulerAngles.y);
        }
    }

    void CheckLowHealthWarning()
    {
        if (lowHealthWarning == null || playerHealth == null) return;

        bool shouldShowWarning = playerHealth.GetHealthPercentage() < 0.3f;
        
        if (shouldShowWarning != isLowHealth)
        {
            isLowHealth = shouldShowWarning;
            
            if (isLowHealth)
            {
                // Start pulsing red warning
                InvokeRepeating(nameof(PulseLowHealthWarning), 0f, 0.5f);
            }
            else
            {
                // Stop warning
                CancelInvoke(nameof(PulseLowHealthWarning));
                lowHealthWarning.color = Color.clear;
            }
        }
    }

    void PulseLowHealthWarning()
    {
        if (lowHealthWarning == null) return;
        
        float alpha = Mathf.PingPong(Time.time * 2f, 0.3f);
        Color color = Color.red;
        color.a = alpha;
        lowHealthWarning.color = color;
    }

    public void ShowHitMarker()
    {
        hitMarkerTimer = hitMarkerDuration;
        if (hitMarker != null)
        {
            hitMarker.color = Color.white;
        }
    }

    public void ShowKillNotification(string killerName, string victimName, string weaponName)
    {
        if (killNotification == null || killNotificationText == null) return;

        killNotificationText.text = $"{killerName} eliminated {victimName} with {weaponName}";
        killNotification.SetActive(true);
        
        // Hide after 3 seconds
        Invoke(nameof(HideKillNotification), 3f);
    }

    void HideKillNotification()
    {
        if (killNotification != null)
            killNotification.SetActive(false);
    }

    public void ShowObjectiveNotification(string message)
    {
        if (objectiveNotification == null || objectiveText == null) return;

        objectiveText.text = message;
        objectiveNotification.SetActive(true);
        
        // Hide after 5 seconds
        Invoke(nameof(HideObjectiveNotification), 5f);
    }

    void HideObjectiveNotification()
    {
        if (objectiveNotification != null)
            objectiveNotification.SetActive(false);
    }

    public void UpdateScore(int teamAScore, int teamBScore)
    {
        if (scoreText != null)
        {
            scoreText.text = $"Team A: {teamAScore} | Team B: {teamBScore}";
        }
    }

    public void UpdateTimer(float timeRemaining)
    {
        if (timerText != null)
        {
            int minutes = Mathf.FloorToInt(timeRemaining / 60);
            int seconds = Mathf.FloorToInt(timeRemaining % 60);
            timerText.text = $"{minutes:00}:{seconds:00}";
        }
    }

    public void UpdateAbilityCooldown(float cooldownPercent)
    {
        if (abilityCooldown != null)
        {
            abilityCooldown.value = 1f - cooldownPercent;
        }
    }

    void UpdateAllUI()
    {
        UpdateHealthUI();
        UpdateWeaponUI();
        UpdateCrosshair();
    }
}

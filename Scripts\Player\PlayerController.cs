using UnityEngine;

public class PlayerController : MonoBehaviour
{
    [Header("Movement Settings")]
    public float walkSpeed = 5f;
    public float runSpeed = 8f;
    public float jumpHeight = 2f;
    public float slideSpeed = 12f;
    public float slideDuration = 1f;
    public float gravity = -9.81f;
    public float groundDrag = 5f;
    public float airDrag = 0.5f;

    [Header("Camera Settings")]
    public Camera playerCamera;
    public float mouseSensitivity = 2f;
    public float maxLookAngle = 80f;

    [Header("Ground Check")]
    public Transform groundCheck;
    public float groundDistance = 0.4f;
    public LayerMask groundMask;

    // Private variables
    private CharacterController controller;
    private Vector3 velocity;
    private bool isGrounded;
    private bool isSliding;
    private float slideTimer;
    private float xRotation = 0f;
    private bool isRunning;

    void Start()
    {
        controller = GetComponent<CharacterController>();
        
        // Lock cursor to center of screen
        Cursor.lockState = CursorLockMode.Locked;
        
        // If no camera assigned, try to find one
        if (playerCamera == null)
            playerCamera = Camera.main;
    }

    void Update()
    {
        HandleMouseLook();
        HandleMovement();
        HandleJump();
        HandleSlide();
    }

    void HandleMouseLook()
    {
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;

        // Rotate the player body left and right
        transform.Rotate(Vector3.up * mouseX);

        // Rotate the camera up and down
        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, -maxLookAngle, maxLookAngle);
        playerCamera.transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
    }

    void HandleMovement()
    {
        // Ground check
        bool wasGrounded = isGrounded;
        isGrounded = Physics.CheckSphere(groundCheck.position, groundDistance, groundMask);

        // Landing sound
        if (isGrounded && !wasGrounded && velocity.y < -5f)
        {
            if (AudioManager.Instance != null)
                AudioManager.Instance.PlaySFX("Land", 0.7f);
        }

        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;
        }

        // Get input
        float x = Input.GetAxis("Horizontal");
        float z = Input.GetAxis("Vertical");

        // Check if running
        isRunning = Input.GetKey(KeyCode.LeftShift) && z > 0;

        // Calculate movement
        Vector3 move = transform.right * x + transform.forward * z;
        bool isMoving = move.magnitude > 0.1f;

        // Determine speed
        float currentSpeed = walkSpeed;
        if (isSliding)
        {
            currentSpeed = slideSpeed;
        }
        else if (isRunning)
        {
            currentSpeed = runSpeed;
        }

        // Apply movement
        controller.Move(move * currentSpeed * Time.deltaTime);

        // Footstep sounds
        if (isGrounded && isMoving && !isSliding)
        {
            HandleFootsteps();
        }

        // Apply gravity
        velocity.y += gravity * Time.deltaTime;
        controller.Move(velocity * Time.deltaTime);

        // Apply drag
        if (isGrounded)
        {
            velocity.x *= (1 - groundDrag * Time.deltaTime);
            velocity.z *= (1 - groundDrag * Time.deltaTime);
        }
        else
        {
            velocity.x *= (1 - airDrag * Time.deltaTime);
            velocity.z *= (1 - airDrag * Time.deltaTime);
        }
    }

    private float footstepTimer = 0f;
    private float footstepInterval = 0.5f;

    void HandleFootsteps()
    {
        footstepTimer += Time.deltaTime;

        float currentInterval = isRunning ? footstepInterval * 0.7f : footstepInterval;

        if (footstepTimer >= currentInterval)
        {
            if (AudioManager.Instance != null)
                AudioManager.Instance.PlayFootstep();
            footstepTimer = 0f;
        }
    }

    void HandleJump()
    {
        if (Input.GetButtonDown("Jump") && isGrounded && !isSliding)
        {
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);

            // Jump sound
            if (AudioManager.Instance != null)
                AudioManager.Instance.PlaySFX("Jump", 0.6f);
        }
    }

    void HandleSlide()
    {
        if (Input.GetKeyDown(KeyCode.C) && isGrounded && isRunning)
        {
            StartSlide();
        }

        if (isSliding)
        {
            slideTimer -= Time.deltaTime;
            if (slideTimer <= 0 || !Input.GetKey(KeyCode.C))
            {
                EndSlide();
            }
        }
    }

    void StartSlide()
    {
        isSliding = true;
        slideTimer = slideDuration;

        // Lower the camera for slide effect
        playerCamera.transform.localPosition = new Vector3(0, -0.5f, 0);

        // Slide sound
        if (AudioManager.Instance != null)
            AudioManager.Instance.PlaySFX("Slide", 0.8f);
    }

    void EndSlide()
    {
        isSliding = false;
        
        // Reset camera position
        playerCamera.transform.localPosition = new Vector3(0, 0, 0);
    }

    void OnDrawGizmosSelected()
    {
        // Draw ground check sphere
        if (groundCheck != null)
        {
            Gizmos.color = isGrounded ? Color.green : Color.red;
            Gizmos.DrawWireSphere(groundCheck.position, groundDistance);
        }
    }
}

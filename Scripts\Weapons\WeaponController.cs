using UnityEngine;
using UnityEngine.UI;

public class WeaponController : MonoBehaviour
{
    [Header("Weapon Settings")]
    public WeaponData weaponData;
    public Transform firePoint;
    public Camera playerCamera;
    
    [Header("UI References")]
    public Text ammoText;
    public Image crosshair;
    
    [Header("Effects")]
    public ParticleSystem muzzleFlash;
    public GameObject impactEffect;
    
    // Private variables
    private int currentAmmo;
    private int reserveAmmo;
    private float nextTimeToFire = 0f;
    private bool isReloading = false;
    private Vector3 originalPosition;
    private Quaternion originalRotation;

    void Start()
    {
        if (weaponData != null)
        {
            currentAmmo = weaponData.magazineSize;
            reserveAmmo = weaponData.maxReserveAmmo;
            originalPosition = transform.localPosition;
            originalRotation = transform.localRotation;
        }
        
        UpdateAmmoUI();
    }

    void Update()
    {
        HandleInput();
        HandleRecoil();
    }

    void HandleInput()
    {
        // Shooting
        if (weaponData.isAutomatic)
        {
            if (Input.GetButton("Fire1") && Time.time >= nextTimeToFire && currentAmmo > 0 && !isReloading)
            {
                Shoot();
            }
        }
        else
        {
            if (Input.GetButtonDown("Fire1") && Time.time >= nextTimeToFire && currentAmmo > 0 && !isReloading)
            {
                Shoot();
            }
        }

        // Reloading
        if (Input.GetKeyDown(KeyCode.R) && currentAmmo < weaponData.magazineSize && reserveAmmo > 0 && !isReloading)
        {
            StartCoroutine(Reload());
        }

        // Auto reload when empty
        if (currentAmmo <= 0 && reserveAmmo > 0 && !isReloading)
        {
            StartCoroutine(Reload());
        }
    }

    void Shoot()
    {
        nextTimeToFire = Time.time + 1f / weaponData.fireRate;
        currentAmmo--;

        // Play muzzle flash
        if (muzzleFlash != null)
            muzzleFlash.Play();

        // Apply recoil
        ApplyRecoil();

        // Raycast for hit detection
        RaycastHit hit;
        Vector3 shootDirection = playerCamera.transform.forward;
        
        // Add spread
        shootDirection = AddSpread(shootDirection);

        if (Physics.Raycast(playerCamera.transform.position, shootDirection, out hit, weaponData.range))
        {
            // Check if we hit a player
            PlayerHealth targetHealth = hit.collider.GetComponent<PlayerHealth>();
            if (targetHealth != null)
            {
                // Calculate damage based on hit location
                float damage = weaponData.damage;
                if (hit.collider.CompareTag("Head"))
                {
                    damage *= weaponData.headShotMultiplier;
                }
                
                targetHealth.TakeDamage(damage);
            }

            // Spawn impact effect
            if (impactEffect != null)
            {
                GameObject impact = Instantiate(impactEffect, hit.point, Quaternion.LookRotation(hit.normal));
                Destroy(impact, 2f);
            }
        }

        UpdateAmmoUI();
    }

    Vector3 AddSpread(Vector3 direction)
    {
        float spread = weaponData.spread;
        direction += new Vector3(
            Random.Range(-spread, spread),
            Random.Range(-spread, spread),
            Random.Range(-spread, spread)
        );
        return direction.normalized;
    }

    void ApplyRecoil()
    {
        // Simple recoil effect - move weapon up and back slightly
        Vector3 recoilOffset = new Vector3(
            Random.Range(-weaponData.recoil, weaponData.recoil),
            weaponData.recoil,
            -weaponData.recoil * 0.5f
        );
        
        transform.localPosition += recoilOffset * 0.1f;
        transform.localRotation *= Quaternion.Euler(-weaponData.recoil * 10f, 0, 0);
    }

    void HandleRecoil()
    {
        // Smoothly return weapon to original position
        transform.localPosition = Vector3.Lerp(transform.localPosition, originalPosition, Time.deltaTime * 10f);
        transform.localRotation = Quaternion.Lerp(transform.localRotation, originalRotation, Time.deltaTime * 10f);
    }

    System.Collections.IEnumerator Reload()
    {
        isReloading = true;
        
        yield return new WaitForSeconds(weaponData.reloadTime);
        
        int ammoNeeded = weaponData.magazineSize - currentAmmo;
        int ammoToReload = Mathf.Min(ammoNeeded, reserveAmmo);
        
        currentAmmo += ammoToReload;
        reserveAmmo -= ammoToReload;
        
        isReloading = false;
        UpdateAmmoUI();
    }

    void UpdateAmmoUI()
    {
        if (ammoText != null)
        {
            ammoText.text = $"{currentAmmo} / {reserveAmmo}";
        }
    }

    public void SetWeaponData(WeaponData newWeaponData)
    {
        weaponData = newWeaponData;
        currentAmmo = weaponData.magazineSize;
        reserveAmmo = weaponData.maxReserveAmmo;
        UpdateAmmoUI();
    }
}

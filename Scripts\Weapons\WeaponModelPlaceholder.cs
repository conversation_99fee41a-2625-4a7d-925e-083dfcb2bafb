using UnityEngine;

public class WeaponModelPlaceholder : MonoBehaviour
{
    [Header("Model Replacement")]
    [<PERSON><PERSON><PERSON>("Drag your 3D weapon model here to replace the placeholder")]
    public GameObject weaponModelPrefab;
    
    [Header("Auto-Replace Settings")]
    public bool autoReplaceOnStart = false;
    
    void Start()
    {
        if (autoReplaceOnStart && weaponModelPrefab != null)
        {
            ReplaceWithModel();
        }
    }
    
    [ContextMenu("Replace with 3D Model")]
    public void ReplaceWithModel()
    {
        if (weaponModelPrefab == null)
        {
            Debug.LogWarning("No weapon model assigned! Drag a 3D model to the 'Weapon Model Prefab' field.");
            return;
        }
        
        // Store important references before replacement
        Transform parent = transform.parent;
        Vector3 position = transform.localPosition;
        Vector3 rotation = transform.localEulerAngles;
        Vector3 scale = transform.localScale;
        
        // Find fire point if it exists
        Transform firePoint = transform.Find("Fire Point");
        Vector3 firePointLocalPos = Vector3.zero;
        if (firePoint != null)
        {
            firePointLocalPos = firePoint.localPosition;
        }
        
        // Create new weapon model
        GameObject newWeapon = Instantiate(weaponModelPrefab, parent);
        newWeapon.name = weaponModelPrefab.name;
        newWeapon.transform.localPosition = position;
        newWeapon.transform.localEulerAngles = rotation;
        newWeapon.transform.localScale = scale;
        
        // Recreate fire point on new model
        GameObject newFirePoint = new GameObject("Fire Point");
        newFirePoint.transform.parent = newWeapon.transform;
        newFirePoint.transform.localPosition = firePointLocalPos;
        
        // Update weapon controller reference
        WeaponController weaponController = parent.GetComponent<WeaponController>();
        if (weaponController != null)
        {
            weaponController.firePoint = newFirePoint.transform;
        }
        
        // Destroy the placeholder
        DestroyImmediate(gameObject);
        
        Debug.Log($"Successfully replaced placeholder with {weaponModelPrefab.name}!");
    }
}

using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;

public class ScoreboardManager : MonoBehaviour
{
    [Header("Scoreboard UI")]
    public GameObject scoreboardPanel;
    public Transform teamAContainer;
    public Transform teamBContainer;
    public GameObject playerScoreEntryPrefab;
    
    [Header("Match Info")]
    public TextMeshProUGUI matchTypeText;
    public TextMeshProUGUI mapNameText;
    public TextMeshProUGUI teamAScoreText;
    public TextMeshProUGUI teamBScoreText;
    public TextMeshProUGUI matchTimerText;
    
    [Header("Player Stats")]
    public TextMeshProUGUI personalKillsText;
    public TextMesh<PERSON><PERSON>UGUI personalDeathsText;
    public TextMeshProUGUI personalAssistsText;
    public TextMeshProUGUI personalKDRText;
    
    [Header("Settings")]
    public KeyCode scoreboardKey = KeyCode.Tab;
    public bool showOnlyWhenPressed = true;
    
    // Private variables
    private List<PlayerScoreEntry> playerEntries = new List<PlayerScoreEntry>();
    private GameManager gameManager;
    private bool isVisible = false;

    [System.Serializable]
    public class PlayerScoreEntry
    {
        public string playerName;
        public int team; // 0 = Team A, 1 = Team B
        public int kills;
        public int deaths;
        public int assists;
        public int score;
        public float kdr;
        public bool isLocalPlayer;
        public GameObject uiElement;
    }

    void Start()
    {
        gameManager = FindObjectOfType<GameManager>();
        
        if (scoreboardPanel != null)
            scoreboardPanel.SetActive(false);
            
        // Initialize with some test data
        InitializeTestData();
    }

    void Update()
    {
        HandleInput();
        UpdateScoreboard();
    }

    void HandleInput()
    {
        if (showOnlyWhenPressed)
        {
            if (Input.GetKeyDown(scoreboardKey))
            {
                ShowScoreboard();
            }
            else if (Input.GetKeyUp(scoreboardKey))
            {
                HideScoreboard();
            }
        }
        else
        {
            if (Input.GetKeyDown(scoreboardKey))
            {
                ToggleScoreboard();
            }
        }
    }

    public void ShowScoreboard()
    {
        if (scoreboardPanel != null)
        {
            scoreboardPanel.SetActive(true);
            isVisible = true;
            RefreshScoreboard();
        }
    }

    public void HideScoreboard()
    {
        if (scoreboardPanel != null)
        {
            scoreboardPanel.SetActive(false);
            isVisible = false;
        }
    }

    public void ToggleScoreboard()
    {
        if (isVisible)
            HideScoreboard();
        else
            ShowScoreboard();
    }

    void UpdateScoreboard()
    {
        if (!isVisible) return;

        // Update match info
        if (gameManager != null)
        {
            if (teamAScoreText != null)
                teamAScoreText.text = gameManager.teamAScore.ToString();
            if (teamBScoreText != null)
                teamBScoreText.text = gameManager.teamBScore.ToString();
        }

        // Update timer
        UpdateMatchTimer();
        
        // Update player stats
        UpdatePersonalStats();
    }

    void UpdateMatchTimer()
    {
        if (matchTimerText != null && gameManager != null)
        {
            float timeRemaining = gameManager.matchDuration - (Time.time - gameManager.matchStartTime);
            if (timeRemaining < 0) timeRemaining = 0;
            
            int minutes = Mathf.FloorToInt(timeRemaining / 60);
            int seconds = Mathf.FloorToInt(timeRemaining % 60);
            matchTimerText.text = $"{minutes:00}:{seconds:00}";
        }
    }

    void UpdatePersonalStats()
    {
        // Find local player entry
        PlayerScoreEntry localPlayer = playerEntries.FirstOrDefault(p => p.isLocalPlayer);
        if (localPlayer != null)
        {
            if (personalKillsText != null)
                personalKillsText.text = localPlayer.kills.ToString();
            if (personalDeathsText != null)
                personalDeathsText.text = localPlayer.deaths.ToString();
            if (personalAssistsText != null)
                personalAssistsText.text = localPlayer.assists.ToString();
            if (personalKDRText != null)
                personalKDRText.text = localPlayer.kdr.ToString("F2");
        }
    }

    public void RefreshScoreboard()
    {
        // Sort players by score
        var teamAPlayers = playerEntries.Where(p => p.team == 0).OrderByDescending(p => p.score);
        var teamBPlayers = playerEntries.Where(p => p.team == 1).OrderByDescending(p => p.score);

        // Clear existing UI elements
        ClearTeamContainers();

        // Populate Team A
        foreach (var player in teamAPlayers)
        {
            CreatePlayerScoreUI(player, teamAContainer);
        }

        // Populate Team B
        foreach (var player in teamBPlayers)
        {
            CreatePlayerScoreUI(player, teamBContainer);
        }
    }

    void ClearTeamContainers()
    {
        if (teamAContainer != null)
        {
            foreach (Transform child in teamAContainer)
            {
                if (child.gameObject != playerScoreEntryPrefab)
                    Destroy(child.gameObject);
            }
        }

        if (teamBContainer != null)
        {
            foreach (Transform child in teamBContainer)
            {
                if (child.gameObject != playerScoreEntryPrefab)
                    Destroy(child.gameObject);
            }
        }
    }

    void CreatePlayerScoreUI(PlayerScoreEntry player, Transform container)
    {
        if (playerScoreEntryPrefab == null || container == null) return;

        GameObject entry = Instantiate(playerScoreEntryPrefab, container);
        
        // Find UI components in the entry
        TextMeshProUGUI nameText = entry.transform.Find("PlayerName")?.GetComponent<TextMeshProUGUI>();
        TextMeshProUGUI killsText = entry.transform.Find("Kills")?.GetComponent<TextMeshProUGUI>();
        TextMeshProUGUI deathsText = entry.transform.Find("Deaths")?.GetComponent<TextMeshProUGUI>();
        TextMeshProUGUI assistsText = entry.transform.Find("Assists")?.GetComponent<TextMeshProUGUI>();
        TextMeshProUGUI scoreText = entry.transform.Find("Score")?.GetComponent<TextMeshProUGUI>();
        TextMeshProUGUI kdrText = entry.transform.Find("KDR")?.GetComponent<TextMeshProUGUI>();
        Image background = entry.GetComponent<Image>();

        // Set values
        if (nameText != null) nameText.text = player.playerName;
        if (killsText != null) killsText.text = player.kills.ToString();
        if (deathsText != null) deathsText.text = player.deaths.ToString();
        if (assistsText != null) assistsText.text = player.assists.ToString();
        if (scoreText != null) scoreText.text = player.score.ToString();
        if (kdrText != null) kdrText.text = player.kdr.ToString("F2");

        // Highlight local player
        if (player.isLocalPlayer && background != null)
        {
            background.color = new Color(1f, 1f, 0f, 0.3f); // Yellow highlight
        }

        player.uiElement = entry;
    }

    public void AddPlayer(string playerName, int team, bool isLocalPlayer = false)
    {
        PlayerScoreEntry newPlayer = new PlayerScoreEntry
        {
            playerName = playerName,
            team = team,
            kills = 0,
            deaths = 0,
            assists = 0,
            score = 0,
            kdr = 0f,
            isLocalPlayer = isLocalPlayer
        };

        playerEntries.Add(newPlayer);
        
        if (isVisible)
            RefreshScoreboard();
    }

    public void UpdatePlayerStats(string playerName, int kills, int deaths, int assists)
    {
        PlayerScoreEntry player = playerEntries.FirstOrDefault(p => p.playerName == playerName);
        if (player != null)
        {
            player.kills = kills;
            player.deaths = deaths;
            player.assists = assists;
            player.score = kills * 100 + assists * 50; // Simple scoring
            player.kdr = deaths > 0 ? (float)kills / deaths : kills;
            
            if (isVisible)
                RefreshScoreboard();
        }
    }

    public void OnPlayerKill(string killerName, string victimName)
    {
        // Update killer stats
        PlayerScoreEntry killer = playerEntries.FirstOrDefault(p => p.playerName == killerName);
        if (killer != null)
        {
            killer.kills++;
            killer.score += 100;
            killer.kdr = killer.deaths > 0 ? (float)killer.kills / killer.deaths : killer.kills;
        }

        // Update victim stats
        PlayerScoreEntry victim = playerEntries.FirstOrDefault(p => p.playerName == victimName);
        if (victim != null)
        {
            victim.deaths++;
            victim.kdr = victim.deaths > 0 ? (float)victim.kills / victim.deaths : victim.kills;
        }

        if (isVisible)
            RefreshScoreboard();
    }

    void InitializeTestData()
    {
        // Add some test players
        AddPlayer("You", 0, true);
        AddPlayer("Player2", 0);
        AddPlayer("Player3", 0);
        AddPlayer("Enemy1", 1);
        AddPlayer("Enemy2", 1);
        AddPlayer("Enemy3", 1);

        // Add some test stats
        UpdatePlayerStats("You", 5, 2, 3);
        UpdatePlayerStats("Player2", 3, 4, 2);
        UpdatePlayerStats("Player3", 7, 1, 1);
        UpdatePlayerStats("Enemy1", 4, 3, 4);
        UpdatePlayerStats("Enemy2", 2, 5, 1);
        UpdatePlayerStats("Enemy3", 6, 2, 2);
    }
}

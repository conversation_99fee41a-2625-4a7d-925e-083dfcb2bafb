using UnityEngine;

public class PlayerAbilities : MonoBehaviour
{
    [System.Serializable]
    public class Ability
    {
        public string name;
        public float cooldown;
        public float duration;
        public AbilityType type;
        public float power;
        [HideInInspector] public float lastUsedTime;
        [HideInInspector] public bool isActive;
        [HideInInspector] public float activeTime;
    }
    
    public enum AbilityType
    {
        DamageBoost,
        SpeedBoost,
        Shield,
        Invisibility,
        Teleport,
        HealthRegen
    }
    
    [Header("Player Abilities")]
    public Ability[] abilities = new Ability[3];
    
    private AdvancedPlayerController playerController;
    private PlayerHealth playerHealth;
    private WeaponManager weaponManager;
    
    void Start()
    {
        playerController = GetComponent<AdvancedPlayerController>();
        playerHealth = GetComponent<PlayerHealth>();
        weaponManager = GetComponentInChildren<WeaponManager>();
        
        InitializeDefaultAbilities();
    }
    
    void Update()
    {
        UpdateActiveAbilities();
    }
    
    void InitializeDefaultAbilities()
    {
        if (abilities[0] == null)
        {
            abilities[0] = new Ability
            {
                name = "Damage Boost",
                cooldown = 30f,
                duration = 10f,
                type = AbilityType.DamageBoost,
                power = 2f
            };
        }
        
        if (abilities[1] == null)
        {
            abilities[1] = new Ability
            {
                name = "Speed Boost",
                cooldown = 25f,
                duration = 8f,
                type = AbilityType.SpeedBoost,
                power = 1.5f
            };
        }
        
        if (abilities[2] == null)
        {
            abilities[2] = new Ability
            {
                name = "Shield Boost",
                cooldown = 20f,
                duration = 0f, // Instant
                type = AbilityType.Shield,
                power = 50f
            };
        }
    }
    
    public void UseAbility(int abilityIndex)
    {
        if (abilityIndex < 0 || abilityIndex >= abilities.Length)
            return;
        
        Ability ability = abilities[abilityIndex];
        if (ability == null)
            return;
        
        // Check cooldown
        if (Time.time - ability.lastUsedTime < ability.cooldown)
            return;
        
        // Use ability
        ability.lastUsedTime = Time.time;
        ability.isActive = true;
        ability.activeTime = 0f;
        
        ActivateAbility(ability);
        
        Debug.Log($"Used ability: {ability.name}");
    }
    
    void ActivateAbility(Ability ability)
    {
        switch (ability.type)
        {
            case AbilityType.DamageBoost:
                ActivateDamageBoost(ability);
                break;
            case AbilityType.SpeedBoost:
                ActivateSpeedBoost(ability);
                break;
            case AbilityType.Shield:
                ActivateShield(ability);
                break;
            case AbilityType.Invisibility:
                ActivateInvisibility(ability);
                break;
            case AbilityType.Teleport:
                ActivateTeleport(ability);
                break;
            case AbilityType.HealthRegen:
                ActivateHealthRegen(ability);
                break;
        }
    }
    
    void ActivateDamageBoost(Ability ability)
    {
        // Boost weapon damage
        if (weaponManager != null)
        {
            WeaponController currentWeapon = weaponManager.GetCurrentWeapon();
            if (currentWeapon != null)
            {
                currentWeapon.damageMultiplier = ability.power;
            }
        }
        
        CreateAbilityEffect(Color.red);
    }
    
    void ActivateSpeedBoost(Ability ability)
    {
        // Boost movement speed
        if (playerController != null)
        {
            playerController.walkSpeed *= ability.power;
            playerController.sprintSpeed *= ability.power;
        }
        
        CreateAbilityEffect(Color.blue);
    }
    
    void ActivateShield(Ability ability)
    {
        // Restore shield
        if (playerHealth != null)
        {
            playerHealth.RestoreShield(ability.power);
        }
        
        CreateAbilityEffect(Color.cyan);
    }
    
    void ActivateInvisibility(Ability ability)
    {
        // Make player semi-transparent
        Renderer[] renderers = GetComponentsInChildren<Renderer>();
        foreach (Renderer renderer in renderers)
        {
            Color color = renderer.material.color;
            color.a = 0.3f;
            renderer.material.color = color;
        }
        
        CreateAbilityEffect(Color.white);
    }
    
    void ActivateTeleport(Ability ability)
    {
        // Teleport forward
        Vector3 teleportDirection = transform.forward * ability.power;
        CharacterController controller = GetComponent<CharacterController>();
        
        if (controller != null)
        {
            controller.enabled = false;
            transform.position += teleportDirection;
            controller.enabled = true;
        }
        
        CreateAbilityEffect(Color.magenta);
    }
    
    void ActivateHealthRegen(Ability ability)
    {
        // Start health regeneration
        if (playerHealth != null)
        {
            playerHealth.StartHealthRegen(ability.power, ability.duration);
        }
        
        CreateAbilityEffect(Color.green);
    }
    
    void UpdateActiveAbilities()
    {
        for (int i = 0; i < abilities.Length; i++)
        {
            Ability ability = abilities[i];
            if (ability == null || !ability.isActive)
                continue;
            
            ability.activeTime += Time.deltaTime;
            
            if (ability.activeTime >= ability.duration)
            {
                DeactivateAbility(ability);
                ability.isActive = false;
            }
        }
    }
    
    void DeactivateAbility(Ability ability)
    {
        switch (ability.type)
        {
            case AbilityType.DamageBoost:
                DeactivateDamageBoost();
                break;
            case AbilityType.SpeedBoost:
                DeactivateSpeedBoost();
                break;
            case AbilityType.Invisibility:
                DeactivateInvisibility();
                break;
        }
    }
    
    void DeactivateDamageBoost()
    {
        if (weaponManager != null)
        {
            WeaponController currentWeapon = weaponManager.GetCurrentWeapon();
            if (currentWeapon != null)
            {
                currentWeapon.damageMultiplier = 1f;
            }
        }
    }
    
    void DeactivateSpeedBoost()
    {
        if (playerController != null)
        {
            playerController.walkSpeed = 6f; // Reset to default
            playerController.sprintSpeed = 12f; // Reset to default
        }
    }
    
    void DeactivateInvisibility()
    {
        Renderer[] renderers = GetComponentsInChildren<Renderer>();
        foreach (Renderer renderer in renderers)
        {
            Color color = renderer.material.color;
            color.a = 1f;
            renderer.material.color = color;
        }
    }
    
    void CreateAbilityEffect(Color effectColor)
    {
        // Create visual effect
        GameObject effect = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        effect.transform.position = transform.position + Vector3.up;
        effect.transform.localScale = Vector3.one * 2f;
        effect.GetComponent<Renderer>().material.color = effectColor;
        effect.GetComponent<Collider>().enabled = false;
        
        // Animate effect
        StartCoroutine(AnimateAbilityEffect(effect));
    }
    
    System.Collections.IEnumerator AnimateAbilityEffect(GameObject effect)
    {
        float duration = 1f;
        float elapsed = 0f;
        Vector3 startScale = effect.transform.localScale;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            
            effect.transform.localScale = Vector3.Lerp(startScale, Vector3.zero, progress);
            
            Color color = effect.GetComponent<Renderer>().material.color;
            color.a = 1f - progress;
            effect.GetComponent<Renderer>().material.color = color;
            
            yield return null;
        }
        
        Destroy(effect);
    }
    
    public float GetAbilityCooldownPercent(int abilityIndex)
    {
        if (abilityIndex < 0 || abilityIndex >= abilities.Length || abilities[abilityIndex] == null)
            return 0f;
        
        Ability ability = abilities[abilityIndex];
        float timeSinceUse = Time.time - ability.lastUsedTime;
        
        if (timeSinceUse >= ability.cooldown)
            return 0f;
        
        return 1f - (timeSinceUse / ability.cooldown);
    }
    
    public bool IsAbilityReady(int abilityIndex)
    {
        return GetAbilityCooldownPercent(abilityIndex) == 0f;
    }
}

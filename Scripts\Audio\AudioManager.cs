using UnityEngine;
using UnityEngine.Audio;
using System.Collections.Generic;

public class AudioManager : MonoBehaviour
{
    [Header("Audio Mixers")]
    public AudioMixerGroup masterMixer;
    public AudioMixerGroup musicMixer;
    public AudioMixerGroup sfxMixer;
    public AudioMixerGroup voiceMixer;
    
    [Header("Music")]
    public AudioClip menuMusic;
    public AudioClip gameplayMusic;
    public AudioClip victoryMusic;
    public AudioClip defeatMusic;
    
    [Header("UI Sounds")]
    public AudioClip buttonClick;
    public AudioClip buttonHover;
    public AudioClip menuOpen;
    public AudioClip menuClose;
    
    [Header("Gameplay Sounds")]
    public AudioClip killSound;
    public AudioClip deathSound;
    public AudioClip respawnSound;
    public AudioClip captureSound;
    public AudioClip lostCaptureSound;
    
    [Header("Weapon Sounds")]
    public AudioClip[] autoRifleSounds;
    public AudioClip[] handCannonSounds;
    public AudioClip[] shotgunSounds;
    public AudioClip[] sniperSounds;
    public AudioClip[] reloadSounds;
    public AudioClip emptyClipSound;
    
    [Header("Ambient Sounds")]
    public AudioClip[] footstepSounds;
    public AudioClip jumpSound;
    public AudioClip landSound;
    public AudioClip slideSound;
    
    // Private variables
    private AudioSource musicSource;
    private AudioSource sfxSource;
    private List<AudioSource> audioPool = new List<AudioSource>();
    private Dictionary<string, AudioClip> soundLibrary = new Dictionary<string, AudioClip>();
    
    public static AudioManager Instance { get; private set; }

    void Awake()
    {
        // Singleton pattern
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeAudioManager();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    void InitializeAudioManager()
    {
        // Create music source
        musicSource = gameObject.AddComponent<AudioSource>();
        musicSource.outputAudioMixerGroup = musicMixer;
        musicSource.loop = true;
        musicSource.playOnAwake = false;
        
        // Create SFX source
        sfxSource = gameObject.AddComponent<AudioSource>();
        sfxSource.outputAudioMixerGroup = sfxMixer;
        sfxSource.playOnAwake = false;
        
        // Create audio pool for multiple simultaneous sounds
        for (int i = 0; i < 10; i++)
        {
            AudioSource pooledSource = gameObject.AddComponent<AudioSource>();
            pooledSource.outputAudioMixerGroup = sfxMixer;
            pooledSource.playOnAwake = false;
            audioPool.Add(pooledSource);
        }
        
        // Build sound library
        BuildSoundLibrary();
        
        // Load audio settings
        LoadAudioSettings();
    }

    void BuildSoundLibrary()
    {
        // Add all sounds to library for easy access
        if (buttonClick != null) soundLibrary["ButtonClick"] = buttonClick;
        if (buttonHover != null) soundLibrary["ButtonHover"] = buttonHover;
        if (menuOpen != null) soundLibrary["MenuOpen"] = menuOpen;
        if (menuClose != null) soundLibrary["MenuClose"] = menuClose;
        if (killSound != null) soundLibrary["Kill"] = killSound;
        if (deathSound != null) soundLibrary["Death"] = deathSound;
        if (respawnSound != null) soundLibrary["Respawn"] = respawnSound;
        if (jumpSound != null) soundLibrary["Jump"] = jumpSound;
        if (landSound != null) soundLibrary["Land"] = landSound;
        if (slideSound != null) soundLibrary["Slide"] = slideSound;
        if (emptyClipSound != null) soundLibrary["EmptyClip"] = emptyClipSound;
    }

    public void PlayMusic(string musicType)
    {
        AudioClip clipToPlay = null;
        
        switch (musicType.ToLower())
        {
            case "menu":
                clipToPlay = menuMusic;
                break;
            case "gameplay":
                clipToPlay = gameplayMusic;
                break;
            case "victory":
                clipToPlay = victoryMusic;
                break;
            case "defeat":
                clipToPlay = defeatMusic;
                break;
        }
        
        if (clipToPlay != null && musicSource != null)
        {
            if (musicSource.clip != clipToPlay)
            {
                musicSource.clip = clipToPlay;
                musicSource.Play();
            }
        }
    }

    public void PlaySFX(string soundName, float volume = 1f, float pitch = 1f)
    {
        if (soundLibrary.ContainsKey(soundName))
        {
            PlaySFX(soundLibrary[soundName], volume, pitch);
        }
        else
        {
            Debug.LogWarning($"Sound '{soundName}' not found in library!");
        }
    }

    public void PlaySFX(AudioClip clip, float volume = 1f, float pitch = 1f)
    {
        if (clip == null) return;
        
        AudioSource availableSource = GetAvailableAudioSource();
        if (availableSource != null)
        {
            availableSource.clip = clip;
            availableSource.volume = volume;
            availableSource.pitch = pitch;
            availableSource.Play();
        }
    }

    public void PlaySFXAtPosition(AudioClip clip, Vector3 position, float volume = 1f)
    {
        if (clip == null) return;
        
        AudioSource.PlayClipAtPoint(clip, position, volume);
    }

    public void PlayWeaponSound(WeaponType weaponType, string soundType = "fire")
    {
        AudioClip[] soundArray = null;
        
        switch (weaponType)
        {
            case WeaponType.AutoRifle:
                soundArray = autoRifleSounds;
                break;
            case WeaponType.HandCannon:
                soundArray = handCannonSounds;
                break;
            case WeaponType.Shotgun:
                soundArray = shotgunSounds;
                break;
            case WeaponType.SniperRifle:
                soundArray = sniperSounds;
                break;
        }
        
        if (soundType == "reload")
        {
            soundArray = reloadSounds;
        }
        
        if (soundArray != null && soundArray.Length > 0)
        {
            AudioClip randomClip = soundArray[Random.Range(0, soundArray.Length)];
            PlaySFX(randomClip);
        }
    }

    public void PlayFootstep()
    {
        if (footstepSounds != null && footstepSounds.Length > 0)
        {
            AudioClip randomFootstep = footstepSounds[Random.Range(0, footstepSounds.Length)];
            PlaySFX(randomFootstep, 0.5f, Random.Range(0.8f, 1.2f));
        }
    }

    AudioSource GetAvailableAudioSource()
    {
        foreach (AudioSource source in audioPool)
        {
            if (!source.isPlaying)
            {
                return source;
            }
        }
        
        // If no available source, use the SFX source
        return sfxSource;
    }

    public void SetMasterVolume(float volume)
    {
        if (masterMixer != null)
        {
            masterMixer.audioMixer.SetFloat("MasterVolume", Mathf.Log10(volume) * 20);
        }
        PlayerPrefs.SetFloat("MasterVolume", volume);
    }

    public void SetMusicVolume(float volume)
    {
        if (musicMixer != null)
        {
            musicMixer.audioMixer.SetFloat("MusicVolume", Mathf.Log10(volume) * 20);
        }
        PlayerPrefs.SetFloat("MusicVolume", volume);
    }

    public void SetSFXVolume(float volume)
    {
        if (sfxMixer != null)
        {
            sfxMixer.audioMixer.SetFloat("SFXVolume", Mathf.Log10(volume) * 20);
        }
        PlayerPrefs.SetFloat("SFXVolume", volume);
    }

    void LoadAudioSettings()
    {
        float masterVol = PlayerPrefs.GetFloat("MasterVolume", 1f);
        float musicVol = PlayerPrefs.GetFloat("MusicVolume", 0.8f);
        float sfxVol = PlayerPrefs.GetFloat("SFXVolume", 1f);
        
        SetMasterVolume(masterVol);
        SetMusicVolume(musicVol);
        SetSFXVolume(sfxVol);
    }

    public void StopMusic()
    {
        if (musicSource != null)
        {
            musicSource.Stop();
        }
    }

    public void PauseMusic()
    {
        if (musicSource != null)
        {
            musicSource.Pause();
        }
    }

    public void ResumeMusic()
    {
        if (musicSource != null)
        {
            musicSource.UnPause();
        }
    }
}

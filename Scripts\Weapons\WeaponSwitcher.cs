using UnityEngine;

public class WeaponSwitcher : MonoBehaviour
{
    [Header("Weapon Slots")]
    public WeaponSlot[] weaponSlots = new WeaponSlot[3];
    
    [Header("Settings")]
    public KeyCode[] switchKeys = { KeyCode.Alpha1, KeyCode.Alpha2, KeyCode.Alpha3 };
    public float switchTime = 0.5f;
    
    private int currentWeaponIndex = 0;
    private bool isSwitching = false;
    private WeaponController currentWeaponController;
    
    [System.Serializable]
    public class WeaponSlot
    {
        public string weaponName;
        public GameObject weaponPrefab;
        public WeaponData weaponData;
        [HideInInspector] public GameObject instantiatedWeapon;
    }

    void Start()
    {
        // Initialize first weapon
        if (weaponSlots.Length > 0 && weaponSlots[0].weaponPrefab != null)
        {
            SwitchToWeapon(0);
        }
    }

    void Update()
    {
        HandleWeaponSwitching();
    }

    void HandleWeaponSwitching()
    {
        if (isSwitching) return;

        // Check for number key presses
        for (int i = 0; i < switchKeys.Length && i < weaponSlots.Length; i++)
        {
            if (Input.GetKeyDown(switchKeys[i]))
            {
                if (i != currentWeaponIndex && weaponSlots[i].weaponPrefab != null)
                {
                    SwitchToWeapon(i);
                }
            }
        }

        // Mouse wheel switching
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll > 0f)
        {
            SwitchToNextWeapon();
        }
        else if (scroll < 0f)
        {
            SwitchToPreviousWeapon();
        }
    }

    public void SwitchToWeapon(int weaponIndex)
    {
        if (weaponIndex < 0 || weaponIndex >= weaponSlots.Length) return;
        if (weaponSlots[weaponIndex].weaponPrefab == null) return;
        if (isSwitching) return;

        StartCoroutine(SwitchWeaponCoroutine(weaponIndex));
    }

    public void SwitchToNextWeapon()
    {
        int nextIndex = (currentWeaponIndex + 1) % weaponSlots.Length;
        
        // Find next available weapon
        for (int i = 0; i < weaponSlots.Length; i++)
        {
            if (weaponSlots[nextIndex].weaponPrefab != null)
            {
                SwitchToWeapon(nextIndex);
                break;
            }
            nextIndex = (nextIndex + 1) % weaponSlots.Length;
        }
    }

    public void SwitchToPreviousWeapon()
    {
        int prevIndex = currentWeaponIndex - 1;
        if (prevIndex < 0) prevIndex = weaponSlots.Length - 1;
        
        // Find previous available weapon
        for (int i = 0; i < weaponSlots.Length; i++)
        {
            if (weaponSlots[prevIndex].weaponPrefab != null)
            {
                SwitchToWeapon(prevIndex);
                break;
            }
            prevIndex--;
            if (prevIndex < 0) prevIndex = weaponSlots.Length - 1;
        }
    }

    System.Collections.IEnumerator SwitchWeaponCoroutine(int newWeaponIndex)
    {
        isSwitching = true;

        // Hide current weapon
        if (currentWeaponController != null)
        {
            currentWeaponController.gameObject.SetActive(false);
        }

        yield return new WaitForSeconds(switchTime * 0.5f);

        // Destroy current weapon if it exists
        if (weaponSlots[currentWeaponIndex].instantiatedWeapon != null)
        {
            Destroy(weaponSlots[currentWeaponIndex].instantiatedWeapon);
        }

        // Create new weapon
        WeaponSlot newSlot = weaponSlots[newWeaponIndex];
        GameObject newWeapon = Instantiate(newSlot.weaponPrefab, transform);
        newSlot.instantiatedWeapon = newWeapon;

        // Setup weapon controller
        currentWeaponController = newWeapon.GetComponent<WeaponController>();
        if (currentWeaponController == null)
        {
            currentWeaponController = newWeapon.AddComponent<WeaponController>();
        }

        // Assign weapon data
        if (newSlot.weaponData != null)
        {
            currentWeaponController.SetWeaponData(newSlot.weaponData);
        }

        // Assign camera reference
        currentWeaponController.playerCamera = Camera.main;

        currentWeaponIndex = newWeaponIndex;

        yield return new WaitForSeconds(switchTime * 0.5f);

        isSwitching = false;

        Debug.Log($"Switched to {newSlot.weaponName}");
    }

    public void AddWeapon(GameObject weaponPrefab, WeaponData weaponData, string weaponName)
    {
        // Find empty slot
        for (int i = 0; i < weaponSlots.Length; i++)
        {
            if (weaponSlots[i].weaponPrefab == null)
            {
                weaponSlots[i].weaponPrefab = weaponPrefab;
                weaponSlots[i].weaponData = weaponData;
                weaponSlots[i].weaponName = weaponName;
                
                Debug.Log($"Added {weaponName} to slot {i + 1}");
                return;
            }
        }
        
        Debug.LogWarning("No empty weapon slots available!");
    }

    public WeaponController GetCurrentWeapon()
    {
        return currentWeaponController;
    }
}

using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class GameManager : MonoBehaviour
{
    [Header("Game Settings")]
    public GameMode currentGameMode = GameMode.TeamDeathmatch;
    public float matchDuration = 600f; // 10 minutes
    public int scoreToWin = 50;
    
    [Header("Teams")]
    public int teamAScore = 0;
    public int teamBScore = 0;
    
    [Header("UI References")]
    public Text scoreText;
    public Text timerText;
    public Text gameOverText;
    public GameObject gameOverPanel;
    
    [Header("Spawn Points")]
    public Transform[] teamASpawns;
    public Transform[] teamBSpawns;
    
    // Private variables
    private float matchTimer;
    private bool gameActive = true;
    private List<PlayerHealth> players = new List<PlayerHealth>();

    public enum GameMode
    {
        TeamDeathmatch,
        FreeForAll,
        Control
    }

    void Start()
    {
        matchTimer = matchDuration;
        UpdateUI();
        
        // Find all players in the scene
        PlayerHealth[] foundPlayers = FindObjectsOfType<PlayerHealth>();
        players.AddRange(foundPlayers);
    }

    void Update()
    {
        if (gameActive)
        {
            UpdateTimer();
            CheckWinConditions();
        }
    }

    void UpdateTimer()
    {
        matchTimer -= Time.deltaTime;
        
        if (matchTimer <= 0)
        {
            matchTimer = 0;
            EndGame("Time's Up!");
        }
        
        UpdateUI();
    }

    void UpdateUI()
    {
        // Update score
        if (scoreText != null)
        {
            switch (currentGameMode)
            {
                case GameMode.TeamDeathmatch:
                    scoreText.text = $"Team A: {teamAScore} | Team B: {teamBScore}";
                    break;
                case GameMode.FreeForAll:
                    scoreText.text = $"Score: {teamAScore}";
                    break;
            }
        }
        
        // Update timer
        if (timerText != null)
        {
            int minutes = Mathf.FloorToInt(matchTimer / 60);
            int seconds = Mathf.FloorToInt(matchTimer % 60);
            timerText.text = $"{minutes:00}:{seconds:00}";
        }
    }

    void CheckWinConditions()
    {
        switch (currentGameMode)
        {
            case GameMode.TeamDeathmatch:
                if (teamAScore >= scoreToWin)
                {
                    EndGame("Team A Wins!");
                }
                else if (teamBScore >= scoreToWin)
                {
                    EndGame("Team B Wins!");
                }
                break;
                
            case GameMode.FreeForAll:
                if (teamAScore >= scoreToWin)
                {
                    EndGame("You Win!");
                }
                break;
        }
    }

    public void OnPlayerDeath(PlayerHealth deadPlayer)
    {
        if (!gameActive) return;

        // Award points based on game mode
        switch (currentGameMode)
        {
            case GameMode.TeamDeathmatch:
                // For now, just award points to team A (player kills award to opposite team)
                teamBScore++;
                break;
                
            case GameMode.FreeForAll:
                // In FFA, deaths don't award points to the player who died
                break;
        }
        
        UpdateUI();
    }

    public void OnPlayerRespawn(PlayerHealth respawnedPlayer)
    {
        // Handle any respawn logic here
        Debug.Log($"Player respawned: {respawnedPlayer.name}");
    }

    public void OnPlayerKill(PlayerHealth killer, PlayerHealth victim)
    {
        if (!gameActive) return;

        // Award points for kills
        switch (currentGameMode)
        {
            case GameMode.TeamDeathmatch:
                // Determine which team gets the point
                teamAScore++; // Simplified - in real game, check killer's team
                break;
                
            case GameMode.FreeForAll:
                teamAScore++; // Player's personal score
                break;
        }
        
        UpdateUI();
    }

    void EndGame(string message)
    {
        gameActive = false;
        
        if (gameOverText != null)
        {
            gameOverText.text = message;
        }
        
        if (gameOverPanel != null)
        {
            gameOverPanel.SetActive(true);
        }
        
        // Unlock cursor
        Cursor.lockState = CursorLockMode.None;
        
        Debug.Log($"Game Over: {message}");
    }

    public void RestartGame()
    {
        // Reset game state
        teamAScore = 0;
        teamBScore = 0;
        matchTimer = matchDuration;
        gameActive = true;
        
        if (gameOverPanel != null)
        {
            gameOverPanel.SetActive(false);
        }
        
        // Lock cursor again
        Cursor.lockState = CursorLockMode.Locked;
        
        // Respawn all players
        foreach (PlayerHealth player in players)
        {
            if (player != null)
            {
                // Force respawn
                player.SendMessage("Respawn", SendMessageOptions.DontRequireReceiver);
            }
        }
        
        UpdateUI();
    }

    public Transform GetRandomSpawnPoint(int team = 0)
    {
        Transform[] spawns = team == 0 ? teamASpawns : teamBSpawns;
        
        if (spawns.Length > 0)
        {
            return spawns[Random.Range(0, spawns.Length)];
        }
        
        return transform; // Fallback to game manager position
    }
}

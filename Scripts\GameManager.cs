using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
using System.Collections.Generic;

public class GameManager : MonoBehaviour
{
    [Header("Game Settings")]
    public GameMode currentGameMode = GameMode.TeamDeathmatch;
    public float matchDuration = 600f; // 10 minutes
    public int scoreToWin = 50;
    public int maxPlayers = 8;

    [Header("Teams")]
    public int teamAScore = 0;
    public int teamBScore = 0;

    [Header("UI References")]
    public HUDManager hudManager;
    public ScoreboardManager scoreboardManager;
    public GameObject gameOverPanel;
    public TextMeshProUGUI gameOverText;
    public TextMeshProUGUI finalScoreText;
    public Button playAgainButton;
    public Button mainMenuButton;

    [Header("Spawn Points")]
    public Transform[] teamASpawns;
    public Transform[] teamBSpawns;

    [Header("Control Points (for Control mode)")]
    public ControlPoint[] controlPoints;

    [Header("Audio")]
    public AudioSource gameAudioSource;
    public AudioClip matchStartSound;
    public AudioClip matchEndSound;
    public AudioClip killSound;

    // Private variables
    private float matchTimer;
    private bool gameActive = true;
    private List<PlayerHealth> players = new List<PlayerHealth>();
    public float matchStartTime { get; private set; }
    private Dictionary<string, PlayerStats> playerStats = new Dictionary<string, PlayerStats>();

    public enum GameMode
    {
        TeamDeathmatch,
        FreeForAll,
        Control,
        Elimination
    }

    [System.Serializable]
    public class PlayerStats
    {
        public string playerName;
        public int kills;
        public int deaths;
        public int assists;
        public int score;
        public int team;
    }

    void Start()
    {
        matchTimer = matchDuration;
        matchStartTime = Time.time;

        // Load game settings from main menu
        LoadGameSettings();

        // Initialize UI
        UpdateUI();

        // Find all players in the scene
        PlayerHealth[] foundPlayers = FindObjectsOfType<PlayerHealth>();
        players.AddRange(foundPlayers);

        // Initialize player stats
        InitializePlayerStats();

        // Play match start sound
        if (gameAudioSource != null && matchStartSound != null)
            gameAudioSource.PlayOneShot(matchStartSound);

        // Start gameplay music
        if (AudioManager.Instance != null)
            AudioManager.Instance.PlayMusic("gameplay");

        Debug.Log($"Match started! Mode: {currentGameMode}, Duration: {matchDuration/60f:F1} minutes");
    }

    void LoadGameSettings()
    {
        // Load settings saved from main menu
        string gameMode = PlayerPrefs.GetString("GameMode", "Team Deathmatch");
        string map = PlayerPrefs.GetString("Map", "Arena Alpha");
        maxPlayers = PlayerPrefs.GetInt("MaxPlayers", 8);

        // Set game mode based on string
        switch (gameMode)
        {
            case "Team Deathmatch":
                currentGameMode = GameMode.TeamDeathmatch;
                scoreToWin = 50;
                break;
            case "Free For All":
                currentGameMode = GameMode.FreeForAll;
                scoreToWin = 25;
                break;
            case "Control":
                currentGameMode = GameMode.Control;
                scoreToWin = 100;
                break;
            case "Elimination":
                currentGameMode = GameMode.Elimination;
                scoreToWin = 6; // Best of 6 rounds
                break;
        }

        Debug.Log($"Loaded game settings - Mode: {gameMode}, Map: {map}, Max Players: {maxPlayers}");
    }

    void InitializePlayerStats()
    {
        // Initialize stats for local player
        playerStats["Player"] = new PlayerStats
        {
            playerName = "Player",
            kills = 0,
            deaths = 0,
            assists = 0,
            score = 0,
            team = 0
        };

        // Add to scoreboard
        if (scoreboardManager != null)
        {
            scoreboardManager.AddPlayer("Player", 0, true);
        }
    }

    void Update()
    {
        if (gameActive)
        {
            UpdateTimer();
            CheckWinConditions();
        }
    }

    void UpdateTimer()
    {
        matchTimer -= Time.deltaTime;
        
        if (matchTimer <= 0)
        {
            matchTimer = 0;
            EndGame("Time's Up!");
        }
        
        UpdateUI();
    }

    void UpdateUI()
    {
        // Update score
        if (scoreText != null)
        {
            switch (currentGameMode)
            {
                case GameMode.TeamDeathmatch:
                    scoreText.text = $"Team A: {teamAScore} | Team B: {teamBScore}";
                    break;
                case GameMode.FreeForAll:
                    scoreText.text = $"Score: {teamAScore}";
                    break;
            }
        }
        
        // Update timer
        if (timerText != null)
        {
            int minutes = Mathf.FloorToInt(matchTimer / 60);
            int seconds = Mathf.FloorToInt(matchTimer % 60);
            timerText.text = $"{minutes:00}:{seconds:00}";
        }
    }

    void CheckWinConditions()
    {
        switch (currentGameMode)
        {
            case GameMode.TeamDeathmatch:
                if (teamAScore >= scoreToWin)
                {
                    EndGame("Team A Wins!");
                }
                else if (teamBScore >= scoreToWin)
                {
                    EndGame("Team B Wins!");
                }
                break;
                
            case GameMode.FreeForAll:
                if (teamAScore >= scoreToWin)
                {
                    EndGame("You Win!");
                }
                break;
        }
    }

    public void OnPlayerDeath(PlayerHealth deadPlayer)
    {
        if (!gameActive) return;

        // Award points based on game mode
        switch (currentGameMode)
        {
            case GameMode.TeamDeathmatch:
                // For now, just award points to team A (player kills award to opposite team)
                teamBScore++;
                break;
                
            case GameMode.FreeForAll:
                // In FFA, deaths don't award points to the player who died
                break;
        }
        
        UpdateUI();
    }

    public void OnPlayerRespawn(PlayerHealth respawnedPlayer)
    {
        // Handle any respawn logic here
        Debug.Log($"Player respawned: {respawnedPlayer.name}");
    }

    public void OnPlayerKill(PlayerHealth killer, PlayerHealth victim)
    {
        if (!gameActive) return;

        // Award points for kills
        switch (currentGameMode)
        {
            case GameMode.TeamDeathmatch:
                // Determine which team gets the point
                teamAScore++; // Simplified - in real game, check killer's team
                break;
                
            case GameMode.FreeForAll:
                teamAScore++; // Player's personal score
                break;
        }
        
        UpdateUI();
    }

    void EndGame(string message)
    {
        gameActive = false;
        
        if (gameOverText != null)
        {
            gameOverText.text = message;
        }
        
        if (gameOverPanel != null)
        {
            gameOverPanel.SetActive(true);
        }
        
        // Unlock cursor
        Cursor.lockState = CursorLockMode.None;
        
        Debug.Log($"Game Over: {message}");
    }

    public void RestartGame()
    {
        // Reset game state
        teamAScore = 0;
        teamBScore = 0;
        matchTimer = matchDuration;
        gameActive = true;
        
        if (gameOverPanel != null)
        {
            gameOverPanel.SetActive(false);
        }
        
        // Lock cursor again
        Cursor.lockState = CursorLockMode.Locked;
        
        // Respawn all players
        foreach (PlayerHealth player in players)
        {
            if (player != null)
            {
                // Force respawn
                player.SendMessage("Respawn", SendMessageOptions.DontRequireReceiver);
            }
        }
        
        UpdateUI();
    }

    public Transform GetRandomSpawnPoint(int team = 0)
    {
        Transform[] spawns = team == 0 ? teamASpawns : teamBSpawns;

        if (spawns.Length > 0)
        {
            return spawns[Random.Range(0, spawns.Length)];
        }

        return transform; // Fallback to game manager position
    }

    public void OnControlPointCaptured(ControlPoint point, int team)
    {
        if (!gameActive) return;

        // Award points for capturing
        if (team == 0)
            teamAScore += 10;
        else
            teamBScore += 10;

        // Show notification
        if (hudManager != null)
        {
            string teamName = team == 0 ? "Team A" : "Team B";
            hudManager.ShowObjectiveNotification($"{teamName} captured {point.pointName}!");
        }

        UpdateUI();

        Debug.Log($"Control point {point.pointName} captured by Team {(team == 0 ? "A" : "B")}");
    }

    public void OnPlayerKill(string killerName, string victimName, string weaponName)
    {
        if (!gameActive) return;

        // Update player stats
        if (playerStats.ContainsKey(killerName))
        {
            playerStats[killerName].kills++;
            playerStats[killerName].score += 100;
        }

        if (playerStats.ContainsKey(victimName))
        {
            playerStats[victimName].deaths++;
        }

        // Update scoreboard
        if (scoreboardManager != null)
        {
            scoreboardManager.OnPlayerKill(killerName, victimName);
        }

        // Show kill notification
        if (hudManager != null)
        {
            hudManager.ShowKillNotification(killerName, victimName, weaponName);
        }

        // Play kill sound
        if (AudioManager.Instance != null)
        {
            AudioManager.Instance.PlaySFX("Kill");
        }

        // Award team points
        switch (currentGameMode)
        {
            case GameMode.TeamDeathmatch:
                teamAScore++; // Simplified - in real game, check killer's team
                break;
            case GameMode.FreeForAll:
                // Individual scoring handled in player stats
                break;
        }

        UpdateUI();
    }
}

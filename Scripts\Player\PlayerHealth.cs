using UnityEngine;
using UnityEngine.UI;

public class PlayerHealth : MonoBehaviour
{
    [Header("Health Settings")]
    public float maxHealth = 100f;
    public float maxShield = 100f;
    public float shieldRegenDelay = 3f;
    public float shieldRegenRate = 25f;
    public float healthRegenDelay = 5f;
    public float healthRegenRate = 10f;
    
    [Header("UI References")]
    public Slider healthBar;
    public Slider shieldBar;
    public Image damageOverlay;
    
    [Header("Respawn Settings")]
    public Transform[] respawnPoints;
    public float respawnDelay = 3f;
    
    // Private variables
    private float currentHealth;
    private float currentShield;
    private float lastDamageTime;
    private bool isDead = false;
    private PlayerController playerController;
    private WeaponController weaponController;

    void Start()
    {
        currentHealth = maxHealth;
        currentShield = maxShield;
        playerController = GetComponent<PlayerController>();
        weaponController = GetComponentInChildren<WeaponController>();
        
        UpdateHealthUI();
    }

    void Update()
    {
        HandleRegeneration();
        UpdateDamageOverlay();
    }

    public void TakeDamage(float damage)
    {
        if (isDead) return;

        lastDamageTime = Time.time;

        // Damage shield first, then health
        if (currentShield > 0)
        {
            float shieldDamage = Mathf.Min(damage, currentShield);
            currentShield -= shieldDamage;
            damage -= shieldDamage;
        }

        if (damage > 0 && currentShield <= 0)
        {
            currentHealth -= damage;
        }

        // Clamp values
        currentHealth = Mathf.Clamp(currentHealth, 0, maxHealth);
        currentShield = Mathf.Clamp(currentShield, 0, maxShield);

        UpdateHealthUI();

        // Check for death
        if (currentHealth <= 0)
        {
            Die();
        }
    }

    void HandleRegeneration()
    {
        if (isDead) return;

        float timeSinceLastDamage = Time.time - lastDamageTime;

        // Shield regeneration
        if (currentShield < maxShield && timeSinceLastDamage >= shieldRegenDelay)
        {
            currentShield += shieldRegenRate * Time.deltaTime;
            currentShield = Mathf.Clamp(currentShield, 0, maxShield);
            UpdateHealthUI();
        }

        // Health regeneration (only if shield is full)
        if (currentHealth < maxHealth && currentShield >= maxShield && timeSinceLastDamage >= healthRegenDelay)
        {
            currentHealth += healthRegenRate * Time.deltaTime;
            currentHealth = Mathf.Clamp(currentHealth, 0, maxHealth);
            UpdateHealthUI();
        }
    }

    void UpdateHealthUI()
    {
        if (healthBar != null)
        {
            healthBar.value = currentHealth / maxHealth;
        }

        if (shieldBar != null)
        {
            shieldBar.value = currentShield / maxShield;
        }
    }

    void UpdateDamageOverlay()
    {
        if (damageOverlay != null)
        {
            float healthPercentage = currentHealth / maxHealth;
            float alpha = Mathf.Lerp(0.5f, 0f, healthPercentage);
            
            Color overlayColor = damageOverlay.color;
            overlayColor.a = alpha;
            damageOverlay.color = overlayColor;
        }
    }

    void Die()
    {
        if (isDead) return;

        isDead = true;
        
        // Disable player controls
        if (playerController != null)
            playerController.enabled = false;
            
        if (weaponController != null)
            weaponController.enabled = false;

        // Start respawn process
        Invoke("Respawn", respawnDelay);
        
        // Notify game manager of death
        GameManager gameManager = FindObjectOfType<GameManager>();
        if (gameManager != null)
        {
            gameManager.OnPlayerDeath(this);
        }
    }

    void Respawn()
    {
        isDead = false;
        
        // Reset health and shield
        currentHealth = maxHealth;
        currentShield = maxShield;
        lastDamageTime = 0f;
        
        // Move to respawn point
        if (respawnPoints.Length > 0)
        {
            Transform spawnPoint = respawnPoints[Random.Range(0, respawnPoints.Length)];
            transform.position = spawnPoint.position;
            transform.rotation = spawnPoint.rotation;
        }
        
        // Re-enable player controls
        if (playerController != null)
            playerController.enabled = true;
            
        if (weaponController != null)
            weaponController.enabled = true;
        
        UpdateHealthUI();
        
        // Notify game manager of respawn
        GameManager gameManager = FindObjectOfType<GameManager>();
        if (gameManager != null)
        {
            gameManager.OnPlayerRespawn(this);
        }
    }

    public bool IsAlive()
    {
        return !isDead;
    }

    public float GetHealthPercentage()
    {
        return currentHealth / maxHealth;
    }

    public float GetShieldPercentage()
    {
        return currentShield / maxShield;
    }
}

using UnityEngine;

public class WeaponManager : MonoBehaviour
{
    [Header("Weapon System")]
    public WeaponController[] weapons;
    public int currentWeaponIndex = 0;
    
    [Header("Weapon Switching")]
    public float switchSpeed = 1f;
    
    private bool isSwitching = false;
    
    void Start()
    {
        // Initialize weapons
        if (weapons == null || weapons.Length == 0)
        {
            weapons = GetComponentsInChildren<WeaponController>();
        }
        
        // Show only current weapon
        for (int i = 0; i < weapons.Length; i++)
        {
            weapons[i].gameObject.SetActive(i == currentWeaponIndex);
        }
    }
    
    void Update()
    {
        HandleWeaponInput();
    }
    
    void HandleWeaponInput()
    {
        // Number key weapon switching
        for (int i = 1; i <= weapons.Length && i <= 9; i++)
        {
            if (Input.GetKeyDown(KeyCode.Alpha0 + i))
            {
                SwitchWeapon(i - 1);
            }
        }
        
        // Mouse wheel weapon switching
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll > 0f)
        {
            SwitchToNextWeapon();
        }
        else if (scroll < 0f)
        {
            SwitchToPreviousWeapon();
        }
    }
    
    public void SwitchWeapon(int weaponIndex)
    {
        if (isSwitching || weaponIndex == currentWeaponIndex || 
            weaponIndex < 0 || weaponIndex >= weapons.Length)
            return;
        
        StartCoroutine(SwitchWeaponCoroutine(weaponIndex));
    }
    
    public void SwitchToNextWeapon()
    {
        int nextIndex = (currentWeaponIndex + 1) % weapons.Length;
        SwitchWeapon(nextIndex);
    }
    
    public void SwitchToPreviousWeapon()
    {
        int prevIndex = (currentWeaponIndex - 1 + weapons.Length) % weapons.Length;
        SwitchWeapon(prevIndex);
    }
    
    System.Collections.IEnumerator SwitchWeaponCoroutine(int newWeaponIndex)
    {
        isSwitching = true;
        
        // Hide current weapon
        if (weapons[currentWeaponIndex] != null)
        {
            weapons[currentWeaponIndex].gameObject.SetActive(false);
        }
        
        // Wait for switch animation
        yield return new WaitForSeconds(switchSpeed * 0.5f);
        
        // Show new weapon
        currentWeaponIndex = newWeaponIndex;
        if (weapons[currentWeaponIndex] != null)
        {
            weapons[currentWeaponIndex].gameObject.SetActive(true);
        }
        
        // Update UI
        UpdateWeaponUI();
        
        yield return new WaitForSeconds(switchSpeed * 0.5f);
        
        isSwitching = false;
    }
    
    void UpdateWeaponUI()
    {
        AdvancedHUDManager hudManager = FindObjectOfType<AdvancedHUDManager>();
        if (hudManager != null && weapons[currentWeaponIndex] != null)
        {
            WeaponData weaponData = weapons[currentWeaponIndex].weaponData;
            if (weaponData != null)
            {
                hudManager.UpdateWeaponDisplay(weaponData.weaponName);
                hudManager.UpdateAmmoDisplay(
                    weapons[currentWeaponIndex].currentAmmo,
                    weapons[currentWeaponIndex].reserveAmmo
                );
            }
        }
    }
    
    public WeaponController GetCurrentWeapon()
    {
        if (currentWeaponIndex >= 0 && currentWeaponIndex < weapons.Length)
        {
            return weapons[currentWeaponIndex];
        }
        return null;
    }
    
    public bool CanFire()
    {
        WeaponController currentWeapon = GetCurrentWeapon();
        return currentWeapon != null && currentWeapon.CanFire() && !isSwitching;
    }
    
    public void Fire()
    {
        if (CanFire())
        {
            GetCurrentWeapon().Fire();
        }
    }
    
    public void Reload()
    {
        WeaponController currentWeapon = GetCurrentWeapon();
        if (currentWeapon != null && !isSwitching)
        {
            currentWeapon.Reload();
        }
    }
}

// Enum for weapon types
public enum WeaponType
{
    AutoRifle,
    HandCannon,
    SniperRifle,
    RocketLauncher,
    Shotgun,
    SMG
}

// Weapon data scriptable object
[CreateAssetMenu(fileName = "New Weapon Data", menuName = "Weapons/Weapon Data")]
public class WeaponData : ScriptableObject
{
    [Header("Basic Info")]
    public string weaponName = "New Weapon";
    public WeaponType weaponType = WeaponType.AutoRifle;
    
    [Header("Damage")]
    public float damage = 25f;
    public float headShotMultiplier = 2f;
    public float range = 100f;
    
    [Header("Fire Rate")]
    public float fireRate = 600f; // Rounds per minute
    public bool isAutomatic = true;
    
    [Header("Ammo")]
    public int magazineSize = 30;
    public int maxReserveAmmo = 180;
    public float reloadTime = 2.5f;
    
    [Header("Accuracy")]
    public float recoil = 1f;
    public float spread = 0.1f;
    
    [Header("Special")]
    public float explosionRadius = 0f; // For rocket launchers
    public bool isPiercing = false; // For sniper rifles
    
    [Header("Audio")]
    public AudioClip fireSound;
    public AudioClip reloadSound;
    public AudioClip emptySound;
    
    [Header("Visual")]
    public GameObject muzzleFlashPrefab;
    public GameObject impactEffectPrefab;
    public GameObject projectilePrefab; // For rockets
}

using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class AdvancedHUDManager : MonoBehaviour
{
    [Header("Health & Shield")]
    public Slider healthBar;
    public Slider shieldBar;
    public TextMeshProUGUI healthText;
    
    [Header("Weapon & Ammo")]
    public TextMeshProUGUI currentWeaponText;
    public TextMeshProUGUI ammoText;
    public Slider reloadBar;
    
    [Header("Crosshair")]
    public Image crosshair;
    public Image hitMarker;
    public TextMeshProUGUI damageNumbers;
    
    [Header("Score & Timer")]
    public TextMeshProUGUI teamAScoreText;
    public TextMeshProUGUI teamBScoreText;
    public TextMeshProUGUI timerText;
    public TextMeshProUGUI gameModeText;
    public TextMeshProUGUI kdText;
    
    [Header("Abilities")]
    public Image[] abilitySlots;
    public Image[] abilityCooldowns;
    
    [Header("Radar")]
    public Transform radarParent;
    public GameObject enemyDotPrefab;
    public GameObject allyDotPrefab;
    
    [Header("Kill Feed")]
    public Transform killFeedParent;
    public GameObject killFeedEntryPrefab;
    
    private PlayerHealth playerHealth;
    private WeaponManager weaponManager;
    private AdvancedGameManager gameManager;
    
    void Start()
    {
        // Find references
        playerHealth = FindObjectOfType<PlayerHealth>();
        weaponManager = FindObjectOfType<WeaponManager>();
        gameManager = FindObjectOfType<AdvancedGameManager>();
        
        // Initialize UI
        InitializeUI();
    }
    
    void Update()
    {
        UpdateHealthShield();
        UpdateWeaponAmmo();
        UpdateScoreTimer();
        UpdateRadar();
        UpdateAbilities();
    }
    
    void InitializeUI()
    {
        if (hitMarker != null)
            hitMarker.gameObject.SetActive(false);
        
        if (reloadBar != null)
            reloadBar.gameObject.SetActive(false);
        
        if (damageNumbers != null)
            damageNumbers.text = "";
    }
    
    void UpdateHealthShield()
    {
        if (playerHealth != null)
        {
            if (healthBar != null)
                healthBar.value = playerHealth.currentHealth / playerHealth.maxHealth;
            
            if (shieldBar != null)
                shieldBar.value = playerHealth.currentShield / playerHealth.maxShield;
            
            if (healthText != null)
                healthText.text = $"{Mathf.Ceil(playerHealth.currentHealth)} HP";
        }
    }
    
    void UpdateWeaponAmmo()
    {
        if (weaponManager != null)
        {
            WeaponController currentWeapon = weaponManager.GetCurrentWeapon();
            if (currentWeapon != null)
            {
                if (ammoText != null)
                {
                    ammoText.text = $"{currentWeapon.currentAmmo} / {currentWeapon.reserveAmmo}";
                }
                
                if (currentWeaponText != null && currentWeapon.weaponData != null)
                {
                    currentWeaponText.text = currentWeapon.weaponData.weaponName;
                }
                
                // Update reload bar
                if (reloadBar != null)
                {
                    if (currentWeapon.isReloading)
                    {
                        reloadBar.gameObject.SetActive(true);
                        reloadBar.value = currentWeapon.reloadProgress;
                    }
                    else
                    {
                        reloadBar.gameObject.SetActive(false);
                    }
                }
            }
        }
    }
    
    void UpdateScoreTimer()
    {
        if (gameManager != null)
        {
            if (teamAScoreText != null)
                teamAScoreText.text = $"Red Team: {gameManager.teamAScore}";
            
            if (teamBScoreText != null)
                teamBScoreText.text = $"Blue Team: {gameManager.teamBScore}";
            
            if (timerText != null)
            {
                int minutes = Mathf.FloorToInt(gameManager.matchTimeRemaining / 60);
                int seconds = Mathf.FloorToInt(gameManager.matchTimeRemaining % 60);
                timerText.text = $"{minutes:00}:{seconds:00}";
            }
            
            if (gameModeText != null && gameManager.currentGameMode != null)
                gameModeText.text = gameManager.currentGameMode.name;
            
            if (kdText != null)
                kdText.text = $"K: {gameManager.playerKills} | D: {gameManager.playerDeaths}";
        }
    }
    
    void UpdateRadar()
    {
        // Update radar with enemy positions (simplified)
        if (radarParent != null)
        {
            // Clear existing dots
            foreach (Transform child in radarParent)
            {
                if (child.name.Contains("Dot"))
                    Destroy(child.gameObject);
            }
            
            // Add enemy dots
            GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
            foreach (GameObject enemy in enemies)
            {
                Vector3 relativePos = transform.position - enemy.transform.position;
                Vector2 radarPos = new Vector2(relativePos.x, relativePos.z) * 2f; // Scale factor
                
                if (radarPos.magnitude < 75f) // Radar range
                {
                    GameObject dot = Instantiate(enemyDotPrefab, radarParent);
                    dot.GetComponent<RectTransform>().anchoredPosition = radarPos;
                }
            }
        }
    }
    
    void UpdateAbilities()
    {
        PlayerAbilities abilities = FindObjectOfType<PlayerAbilities>();
        if (abilities != null && abilitySlots != null)
        {
            for (int i = 0; i < abilitySlots.Length && i < abilities.abilities.Length; i++)
            {
                if (abilityCooldowns[i] != null)
                {
                    float cooldownPercent = abilities.GetAbilityCooldownPercent(i);
                    abilityCooldowns[i].fillAmount = cooldownPercent;
                    abilityCooldowns[i].gameObject.SetActive(cooldownPercent > 0);
                }
            }
        }
    }
    
    public void UpdateWeaponDisplay(string weaponName)
    {
        if (currentWeaponText != null)
            currentWeaponText.text = weaponName;
    }
    
    public void UpdateAmmoDisplay(int currentAmmo, int reserveAmmo)
    {
        if (ammoText != null)
            ammoText.text = $"{currentAmmo} / {reserveAmmo}";
    }
    
    public void ShowHitMarker()
    {
        if (hitMarker != null)
        {
            hitMarker.gameObject.SetActive(true);
            Invoke("HideHitMarker", 0.2f);
        }
    }
    
    void HideHitMarker()
    {
        if (hitMarker != null)
            hitMarker.gameObject.SetActive(false);
    }
    
    public void ShowDamageNumber(float damage, Vector3 worldPosition)
    {
        if (damageNumbers != null)
        {
            damageNumbers.text = Mathf.Ceil(damage).ToString();
            damageNumbers.gameObject.SetActive(true);
            
            // Convert world position to screen position
            Vector3 screenPos = Camera.main.WorldToScreenPoint(worldPosition);
            damageNumbers.transform.position = screenPos;
            
            Invoke("HideDamageNumbers", 1f);
        }
    }
    
    void HideDamageNumbers()
    {
        if (damageNumbers != null)
            damageNumbers.text = "";
    }
    
    public void AddKillFeedEntry(string killerName, string victimName, string weaponName)
    {
        if (killFeedParent != null && killFeedEntryPrefab != null)
        {
            GameObject entry = Instantiate(killFeedEntryPrefab, killFeedParent);
            TextMeshProUGUI entryText = entry.GetComponent<TextMeshProUGUI>();
            if (entryText != null)
            {
                entryText.text = $"{killerName} [{weaponName}] {victimName}";
            }
            
            // Remove old entries
            if (killFeedParent.childCount > 5)
            {
                Destroy(killFeedParent.GetChild(0).gameObject);
            }
            
            // Auto-remove after 5 seconds
            Destroy(entry, 5f);
        }
    }
}

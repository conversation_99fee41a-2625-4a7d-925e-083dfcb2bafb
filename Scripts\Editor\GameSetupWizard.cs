using UnityEngine;
using UnityEditor;

public class GameSetupWizard : EditorWindow
{
    private bool playerSetup = false;
    private bool weaponSetup = false;
    private bool uiSetup = false;
    private bool aiSetup = false;
    private bool sceneSetup = false;

    [MenuItem("Tools/Game Setup Wizard")]
    public static void ShowWindow()
    {
        GetWindow<GameSetupWizard>("Game Setup Wizard");
    }

    void OnGUI()
    {
        GUILayout.Label("Destiny 2-Inspired PVP Game Setup", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("This wizard will help you set up your game scene.", EditorStyles.helpBox);
        GUILayout.Space(10);

        // Scene Setup
        GUILayout.Label("Scene Setup", EditorStyles.boldLabel);
        if (GUILayout.Button("Create Basic Scene"))
        {
            CreateBasicScene();
            sceneSetup = true;
        }
        if (sceneSetup) GUILayout.Label("✓ Scene created", EditorStyles.miniLabel);
        GUILayout.Space(5);

        // Player Setup
        GUILayout.Label("Player Setup", EditorStyles.boldLabel);
        if (GUILayout.Button("Create Player"))
        {
            CreatePlayer();
            playerSetup = true;
        }
        if (playerSetup) GUILayout.Label("✓ Player created", EditorStyles.miniLabel);
        GUILayout.Space(5);

        // Weapon Setup
        GUILayout.Label("Weapon Setup", EditorStyles.boldLabel);
        if (GUILayout.Button("Create Weapon System"))
        {
            CreateWeaponSystem();
            weaponSetup = true;
        }
        if (weaponSetup) GUILayout.Label("✓ Weapon system created", EditorStyles.miniLabel);
        GUILayout.Space(5);

        // UI Setup
        GUILayout.Label("UI Setup", EditorStyles.boldLabel);
        if (GUILayout.Button("Create Game UI"))
        {
            CreateGameUI();
            uiSetup = true;
        }
        if (uiSetup) GUILayout.Label("✓ UI created", EditorStyles.miniLabel);
        GUILayout.Space(5);

        // AI Setup
        GUILayout.Label("AI Setup", EditorStyles.boldLabel);
        if (GUILayout.Button("Create AI Enemy"))
        {
            CreateAIEnemy();
            aiSetup = true;
        }
        if (aiSetup) GUILayout.Label("✓ AI enemy created", EditorStyles.miniLabel);
        GUILayout.Space(10);

        if (GUILayout.Button("Setup Everything"))
        {
            CreateBasicScene();
            CreatePlayer();
            CreateWeaponSystem();
            CreateGameUI();
            CreateAIEnemy();
            
            playerSetup = weaponSetup = uiSetup = aiSetup = sceneSetup = true;
            
            Debug.Log("Game setup complete! Check the scene hierarchy.");
        }
    }

    void CreateBasicScene()
    {
        // Create terrain
        GameObject terrain = Terrain.CreateTerrainGameObject(null);
        terrain.name = "Game Terrain";

        // Create lighting
        GameObject light = new GameObject("Directional Light");
        Light lightComponent = light.AddComponent<Light>();
        lightComponent.type = LightType.Directional;
        light.transform.rotation = Quaternion.Euler(50, -30, 0);

        // Create spawn points
        GameObject spawnParent = new GameObject("Spawn Points");
        for (int i = 0; i < 4; i++)
        {
            GameObject spawn = new GameObject($"Spawn Point {i + 1}");
            spawn.transform.parent = spawnParent.transform;
            spawn.transform.position = new Vector3(i * 10, 1, 0);
        }

        // Create game manager
        GameObject gameManager = new GameObject("Game Manager");
        gameManager.AddComponent<GameManager>();
    }

    void CreatePlayer()
    {
        GameObject player = new GameObject("Player");
        player.tag = "Player";
        player.layer = LayerMask.NameToLayer("Default");

        // Add CharacterController
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.height = 2f;
        controller.radius = 0.5f;

        // Add PlayerController
        PlayerController playerController = player.AddComponent<PlayerController>();

        // Add PlayerHealth
        PlayerHealth playerHealth = player.AddComponent<PlayerHealth>();

        // Create camera
        GameObject cameraObj = new GameObject("Player Camera");
        cameraObj.transform.parent = player.transform;
        cameraObj.transform.localPosition = new Vector3(0, 1.6f, 0);
        Camera camera = cameraObj.AddComponent<Camera>();
        camera.tag = "MainCamera";

        // Assign camera to player controller
        playerController.playerCamera = camera;

        // Create ground check
        GameObject groundCheck = new GameObject("Ground Check");
        groundCheck.transform.parent = player.transform;
        groundCheck.transform.localPosition = new Vector3(0, -1f, 0);
        playerController.groundCheck = groundCheck.transform;

        // Position player
        player.transform.position = new Vector3(0, 1, 0);

        Debug.Log("Player created successfully!");
    }

    void CreateWeaponSystem()
    {
        GameObject player = GameObject.FindWithTag("Player");
        if (player == null)
        {
            Debug.LogError("Player not found! Create player first.");
            return;
        }

        // Create weapon holder
        GameObject weaponHolder = new GameObject("Weapon Holder");
        weaponHolder.transform.parent = player.transform;
        weaponHolder.transform.localPosition = new Vector3(0.5f, 1.2f, 0.5f);

        // Create weapon model (simple cube for now - replace with 3D model later)
        GameObject weapon = GameObject.CreatePrimitive(PrimitiveType.Cube);
        weapon.name = "Weapon_Placeholder";
        weapon.transform.parent = weaponHolder.transform;
        weapon.transform.localPosition = Vector3.zero;
        weapon.transform.localScale = new Vector3(0.1f, 0.1f, 0.5f);

        // Add a note component for easy identification
        weapon.AddComponent<WeaponModelPlaceholder>();

        // Add weapon controller
        WeaponController weaponController = weaponHolder.AddComponent<WeaponController>();

        // Create fire point
        GameObject firePoint = new GameObject("Fire Point");
        firePoint.transform.parent = weapon.transform;
        firePoint.transform.localPosition = new Vector3(0, 0, 0.5f);
        weaponController.firePoint = firePoint.transform;

        // Assign camera
        weaponController.playerCamera = player.GetComponentInChildren<Camera>();

        Debug.Log("Weapon system created successfully!");
        Debug.Log("Replace the cube with a 3D weapon model from the Asset Store!");
    }

    void CreateGameUI()
    {
        // Create Canvas
        GameObject canvas = new GameObject("Game Canvas");
        Canvas canvasComponent = canvas.AddComponent<Canvas>();
        canvasComponent.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.AddComponent<CanvasScaler>();
        canvas.AddComponent<GraphicRaycaster>();

        // Create health bar
        GameObject healthBar = new GameObject("Health Bar");
        healthBar.transform.parent = canvas.transform;
        // Add Slider component and configure...

        // Create ammo counter
        GameObject ammoText = new GameObject("Ammo Text");
        ammoText.transform.parent = canvas.transform;
        // Add Text component and configure...

        Debug.Log("Game UI created successfully!");
    }

    void CreateAIEnemy()
    {
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "AI Enemy";
        enemy.tag = "Enemy";
        enemy.transform.position = new Vector3(10, 1, 10);

        // Add NavMeshAgent
        enemy.AddComponent<UnityEngine.AI.NavMeshAgent>();

        // Add SimpleAI
        SimpleAI ai = enemy.AddComponent<SimpleAI>();

        // Add PlayerHealth for enemy
        PlayerHealth enemyHealth = enemy.AddComponent<PlayerHealth>();

        // Create fire point
        GameObject firePoint = new GameObject("Fire Point");
        firePoint.transform.parent = enemy.transform;
        firePoint.transform.localPosition = new Vector3(0, 1, 0.5f);
        ai.firePoint = firePoint.transform;

        Debug.Log("AI Enemy created successfully!");
        Debug.Log("Don't forget to bake the NavMesh! (Window > AI > Navigation)");
    }
}

using UnityEngine;
using UnityEditor;

public class GameSetupWizard : EditorWindow
{
    private bool playerSetup = false;
    private bool weaponSetup = false;
    private bool uiSetup = false;
    private bool aiSetup = false;
    private bool sceneSetup = false;

    [MenuItem("Tools/Game Setup Wizard")]
    public static void ShowWindow()
    {
        GetWindow<GameSetupWizard>("Game Setup Wizard");
    }

    void OnGUI()
    {
        GUILayout.Label("Destiny 2-Inspired PVP Game Setup", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("This wizard will help you set up your game scene.", EditorStyles.helpBox);
        GUILayout.Space(10);

        // Scene Setup
        GUILayout.Label("Scene Setup", EditorStyles.boldLabel);
        if (GUILayout.Button("Create Basic Scene"))
        {
            CreateBasicScene();
            sceneSetup = true;
        }
        if (sceneSetup) GUILayout.Label("✓ Scene created", EditorStyles.miniLabel);
        GUILayout.Space(5);

        // Player Setup
        GUILayout.Label("Player Setup", EditorStyles.boldLabel);
        if (GUILayout.Button("Create Player"))
        {
            CreatePlayer();
            playerSetup = true;
        }
        if (playerSetup) GUILayout.Label("✓ Player created", EditorStyles.miniLabel);
        GUILayout.Space(5);

        // Weapon Setup
        GUILayout.Label("Weapon Setup", EditorStyles.boldLabel);
        if (GUILayout.Button("Create Weapon System"))
        {
            CreateWeaponSystem();
            weaponSetup = true;
        }
        if (weaponSetup) GUILayout.Label("✓ Weapon system created", EditorStyles.miniLabel);
        GUILayout.Space(5);

        // UI Setup
        GUILayout.Label("UI Setup", EditorStyles.boldLabel);
        if (GUILayout.Button("Create Game UI"))
        {
            CreateGameUI();
            uiSetup = true;
        }
        if (uiSetup) GUILayout.Label("✓ UI created", EditorStyles.miniLabel);
        GUILayout.Space(5);

        // AI Setup
        GUILayout.Label("AI Setup", EditorStyles.boldLabel);
        if (GUILayout.Button("Create AI Enemy"))
        {
            CreateAIEnemy();
            aiSetup = true;
        }
        if (aiSetup) GUILayout.Label("✓ AI enemy created", EditorStyles.miniLabel);
        GUILayout.Space(10);

        if (GUILayout.Button("Setup Everything"))
        {
            CreateBasicScene();
            CreatePlayer();
            CreateWeaponSystem();
            CreateCompleteGameUI();
            CreateAIEnemy();
            CreateAudioManager();
            CreateControlPoints();

            playerSetup = weaponSetup = uiSetup = aiSetup = sceneSetup = true;

            Debug.Log("Complete game setup finished! Your Destiny-inspired PVP game is ready!");
            Debug.Log("Don't forget to:");
            Debug.Log("1. Bake NavMesh (Window > AI > Navigation)");
            Debug.Log("2. Create weapon data assets");
            Debug.Log("3. Assign UI elements in the HUD Manager");
            Debug.Log("4. Test the game!");
        }
    }

    void CreateBasicScene()
    {
        // Create terrain
        GameObject terrain = Terrain.CreateTerrainGameObject(null);
        terrain.name = "Game Terrain";

        // Create lighting
        GameObject light = new GameObject("Directional Light");
        Light lightComponent = light.AddComponent<Light>();
        lightComponent.type = LightType.Directional;
        light.transform.rotation = Quaternion.Euler(50, -30, 0);

        // Create spawn points
        GameObject spawnParent = new GameObject("Spawn Points");
        for (int i = 0; i < 4; i++)
        {
            GameObject spawn = new GameObject($"Spawn Point {i + 1}");
            spawn.transform.parent = spawnParent.transform;
            spawn.transform.position = new Vector3(i * 10, 1, 0);
        }

        // Create game manager
        GameObject gameManager = new GameObject("Game Manager");
        gameManager.AddComponent<GameManager>();
    }

    void CreatePlayer()
    {
        GameObject player = new GameObject("Player");
        player.tag = "Player";
        player.layer = LayerMask.NameToLayer("Default");

        // Add CharacterController
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.height = 2f;
        controller.radius = 0.5f;

        // Add PlayerController
        PlayerController playerController = player.AddComponent<PlayerController>();

        // Add PlayerHealth
        PlayerHealth playerHealth = player.AddComponent<PlayerHealth>();

        // Create camera
        GameObject cameraObj = new GameObject("Player Camera");
        cameraObj.transform.parent = player.transform;
        cameraObj.transform.localPosition = new Vector3(0, 1.6f, 0);
        Camera camera = cameraObj.AddComponent<Camera>();
        camera.tag = "MainCamera";

        // Assign camera to player controller
        playerController.playerCamera = camera;

        // Create ground check
        GameObject groundCheck = new GameObject("Ground Check");
        groundCheck.transform.parent = player.transform;
        groundCheck.transform.localPosition = new Vector3(0, -1f, 0);
        playerController.groundCheck = groundCheck.transform;

        // Position player
        player.transform.position = new Vector3(0, 1, 0);

        Debug.Log("Player created successfully!");
    }

    void CreateWeaponSystem()
    {
        GameObject player = GameObject.FindWithTag("Player");
        if (player == null)
        {
            Debug.LogError("Player not found! Create player first.");
            return;
        }

        // Create weapon holder
        GameObject weaponHolder = new GameObject("Weapon Holder");
        weaponHolder.transform.parent = player.transform;
        weaponHolder.transform.localPosition = new Vector3(0.5f, 1.2f, 0.5f);

        // Create weapon model (simple cube for now - replace with 3D model later)
        GameObject weapon = GameObject.CreatePrimitive(PrimitiveType.Cube);
        weapon.name = "Weapon_Placeholder";
        weapon.transform.parent = weaponHolder.transform;
        weapon.transform.localPosition = Vector3.zero;
        weapon.transform.localScale = new Vector3(0.1f, 0.1f, 0.5f);

        // Add a note component for easy identification
        weapon.AddComponent<WeaponModelPlaceholder>();

        // Add weapon controller
        WeaponController weaponController = weaponHolder.AddComponent<WeaponController>();

        // Create fire point
        GameObject firePoint = new GameObject("Fire Point");
        firePoint.transform.parent = weapon.transform;
        firePoint.transform.localPosition = new Vector3(0, 0, 0.5f);
        weaponController.firePoint = firePoint.transform;

        // Assign camera
        weaponController.playerCamera = player.GetComponentInChildren<Camera>();

        Debug.Log("Weapon system created successfully!");
        Debug.Log("Replace the cube with a 3D weapon model from the Asset Store!");
    }

    void CreateCompleteGameUI()
    {
        // Create main game canvas
        GameObject canvas = new GameObject("Game Canvas");
        Canvas canvasComponent = canvas.AddComponent<Canvas>();
        canvasComponent.renderMode = RenderMode.ScreenSpaceOverlay;
        CanvasScaler scaler = canvas.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        canvas.AddComponent<GraphicRaycaster>();

        // Create HUD Manager
        GameObject hudManager = new GameObject("HUD Manager");
        hudManager.transform.parent = canvas.transform;
        HUDManager hudComponent = hudManager.AddComponent<HUDManager>();

        // Create Scoreboard Manager
        GameObject scoreboardManager = new GameObject("Scoreboard Manager");
        scoreboardManager.transform.parent = canvas.transform;
        ScoreboardManager scoreboardComponent = scoreboardManager.AddComponent<ScoreboardManager>();

        // Create basic UI elements (you'll need to position these properly)
        CreateUIElement("Health Bar", canvas.transform, typeof(UnityEngine.UI.Slider));
        CreateUIElement("Shield Bar", canvas.transform, typeof(UnityEngine.UI.Slider));
        CreateUIElement("Ammo Text", canvas.transform, typeof(TMPro.TextMeshProUGUI));
        CreateUIElement("Crosshair", canvas.transform, typeof(UnityEngine.UI.Image));
        CreateUIElement("Score Text", canvas.transform, typeof(TMPro.TextMeshProUGUI));
        CreateUIElement("Timer Text", canvas.transform, typeof(TMPro.TextMeshProUGUI));

        Debug.Log("Complete game UI created successfully!");
        Debug.Log("Remember to assign UI elements to the HUD Manager component!");
    }

    GameObject CreateUIElement(string name, Transform parent, System.Type componentType)
    {
        GameObject element = new GameObject(name);
        element.transform.parent = parent;
        element.AddComponent<RectTransform>();
        element.AddComponent(componentType);
        return element;
    }

    void CreateAudioManager()
    {
        GameObject audioManager = new GameObject("Audio Manager");
        audioManager.AddComponent<AudioManager>();

        Debug.Log("Audio Manager created successfully!");
    }

    void CreateControlPoints()
    {
        GameObject controlPointsParent = new GameObject("Control Points");

        // Create 3 control points
        for (int i = 0; i < 3; i++)
        {
            GameObject controlPoint = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            controlPoint.name = $"Control Point {(char)('A' + i)}";
            controlPoint.transform.parent = controlPointsParent.transform;
            controlPoint.transform.position = new Vector3(i * 20 - 20, 0.5f, 0);
            controlPoint.transform.localScale = new Vector3(4, 0.5f, 4);

            ControlPoint cpComponent = controlPoint.AddComponent<ControlPoint>();
            cpComponent.pointName = ((char)('A' + i)).ToString();
            cpComponent.captureRadius = 5f;

            // Create capture UI
            GameObject captureUI = new GameObject("Capture UI");
            captureUI.transform.parent = controlPoint.transform;
            captureUI.transform.localPosition = Vector3.up * 2f;

            Canvas uiCanvas = captureUI.AddComponent<Canvas>();
            uiCanvas.renderMode = RenderMode.WorldSpace;
            uiCanvas.worldCamera = Camera.main;

            cpComponent.captureUI = captureUI;
        }

        Debug.Log("Control Points created successfully!");
    }

    void CreateAIEnemy()
    {
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "AI Enemy";
        enemy.tag = "Enemy";
        enemy.transform.position = new Vector3(10, 1, 10);

        // Add NavMeshAgent
        enemy.AddComponent<UnityEngine.AI.NavMeshAgent>();

        // Add SimpleAI
        SimpleAI ai = enemy.AddComponent<SimpleAI>();

        // Add PlayerHealth for enemy
        PlayerHealth enemyHealth = enemy.AddComponent<PlayerHealth>();

        // Create fire point
        GameObject firePoint = new GameObject("Fire Point");
        firePoint.transform.parent = enemy.transform;
        firePoint.transform.localPosition = new Vector3(0, 1, 0.5f);
        ai.firePoint = firePoint.transform;

        Debug.Log("AI Enemy created successfully!");
        Debug.Log("Don't forget to bake the NavMesh! (Window > AI > Navigation)");
    }
}

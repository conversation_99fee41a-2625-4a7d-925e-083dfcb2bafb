using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using TMPro;
using UnityEngine.AI;

public class DragDropGameSetup : EditorWindow
{
    [Header("Drag & Drop Setup")]
    public GameObject playerPrefab;
    public GameObject weaponPrefab;
    public GameObject enemyPrefab;
    public Material[] teamMaterials = new Material[2];
    public AudioClip[] weaponSounds;
    public Texture2D[] uiTextures;

    private Vector2 scrollPosition;
    private bool setupComplete = false;

    [MenuItem("Tools/Drag & Drop Game Setup")]
    public static void ShowWindow()
    {
        DragDropGameSetup window = GetWindow<DragDropGameSetup>("Drag & Drop Setup");
        window.minSize = new Vector2(400, 600);
    }

    void OnGUI()
    {
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
        GUILayout.Label("🎮 Destiny PVP Game - Drag & Drop Setup", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        EditorGUILayout.HelpBox("Simply drag your assets here and click 'Create Complete Game'!\nNo manual setup required!", MessageType.Info);
        GUILayout.Space(10);

        // Player Setup Section
        DrawSection("👤 Player Setup (Optional)", () => {
            playerPrefab = (GameObject)EditorGUILayout.ObjectField("Custom Player Model", playerPrefab, typeof(GameObject), false);
            EditorGUILayout.HelpBox("Leave empty to use default player setup", MessageType.None);
        });

        // Weapon Setup Section
        DrawSection("🔫 Weapon Setup (Optional)", () => {
            weaponPrefab = (GameObject)EditorGUILayout.ObjectField("Weapon 3D Model", weaponPrefab, typeof(GameObject), false);
            
            GUILayout.Label("Weapon Sounds:");
            if (weaponSounds == null) weaponSounds = new AudioClip[3];
            weaponSounds[0] = (AudioClip)EditorGUILayout.ObjectField("Fire Sound", weaponSounds[0], typeof(AudioClip), false);
            weaponSounds[1] = (AudioClip)EditorGUILayout.ObjectField("Reload Sound", weaponSounds[1], typeof(AudioClip), false);
            weaponSounds[2] = (AudioClip)EditorGUILayout.ObjectField("Empty Sound", weaponSounds[2], typeof(AudioClip), false);
            
            EditorGUILayout.HelpBox("Drag weapon models and sounds here, or leave empty for defaults", MessageType.None);
        });

        // Enemy Setup Section
        DrawSection("🤖 Enemy Setup (Optional)", () => {
            enemyPrefab = (GameObject)EditorGUILayout.ObjectField("Enemy 3D Model", enemyPrefab, typeof(GameObject), false);
            EditorGUILayout.HelpBox("Leave empty to use default enemy capsule", MessageType.None);
        });

        // Materials Section
        DrawSection("🎨 Team Materials (Optional)", () => {
            if (teamMaterials == null) teamMaterials = new Material[2];
            teamMaterials[0] = (Material)EditorGUILayout.ObjectField("Team A Material", teamMaterials[0], typeof(Material), false);
            teamMaterials[1] = (Material)EditorGUILayout.ObjectField("Team B Material", teamMaterials[1], typeof(Material), false);
            EditorGUILayout.HelpBox("Materials for team colors and UI elements", MessageType.None);
        });

        // UI Textures Section
        DrawSection("🖼️ UI Textures (Optional)", () => {
            if (uiTextures == null) uiTextures = new Texture2D[4];
            uiTextures[0] = (Texture2D)EditorGUILayout.ObjectField("Crosshair", uiTextures[0], typeof(Texture2D), false);
            uiTextures[1] = (Texture2D)EditorGUILayout.ObjectField("Health Bar", uiTextures[1], typeof(Texture2D), false);
            uiTextures[2] = (Texture2D)EditorGUILayout.ObjectField("Shield Bar", uiTextures[2], typeof(Texture2D), false);
            uiTextures[3] = (Texture2D)EditorGUILayout.ObjectField("Background", uiTextures[3], typeof(Texture2D), false);
            EditorGUILayout.HelpBox("Custom UI textures for a unique look", MessageType.None);
        });

        GUILayout.Space(20);

        // Main Setup Button
        GUI.backgroundColor = Color.green;
        if (GUILayout.Button("🚀 CREATE COMPLETE GAME", GUILayout.Height(50)))
        {
            CreateCompleteGame();
        }
        GUI.backgroundColor = Color.white;

        if (setupComplete)
        {
            EditorGUILayout.HelpBox("✅ Game setup complete! Press Play to test your game!", MessageType.Info);
            
            if (GUILayout.Button("🎮 Start Game (Play Mode)"))
            {
                EditorApplication.isPlaying = true;
            }
        }

        GUILayout.Space(10);
        
        if (GUILayout.Button("📖 Open Setup Guide"))
        {
            Application.OpenURL("https://docs.unity3d.com/Manual/");
        }

        EditorGUILayout.EndScrollView();
    }

    void DrawSection(string title, System.Action content)
    {
        GUILayout.Space(5);
        EditorGUILayout.LabelField(title, EditorStyles.boldLabel);
        EditorGUI.indentLevel++;
        content();
        EditorGUI.indentLevel--;
        GUILayout.Space(5);
    }

    void CreateCompleteGame()
    {
        EditorUtility.DisplayProgressBar("Creating Game", "Setting up scene...", 0.1f);
        
        try
        {
            // Create basic scene
            CreateScene();
            EditorUtility.DisplayProgressBar("Creating Game", "Creating player...", 0.2f);
            
            // Create player
            GameObject player = CreatePlayer();
            EditorUtility.DisplayProgressBar("Creating Game", "Setting up weapons...", 0.4f);
            
            // Create weapon system
            CreateWeaponSystem(player);
            EditorUtility.DisplayProgressBar("Creating Game", "Building UI...", 0.6f);
            
            // Create complete UI
            CreateCompleteUI();
            EditorUtility.DisplayProgressBar("Creating Game", "Adding enemies...", 0.7f);
            
            // Create enemies
            CreateEnemies();
            EditorUtility.DisplayProgressBar("Creating Game", "Setting up audio...", 0.8f);
            
            // Create audio system
            CreateAudioSystem();
            EditorUtility.DisplayProgressBar("Creating Game", "Adding game manager...", 0.9f);
            
            // Create game manager
            CreateGameManager();
            
            // Final setup
            FinalizeSetup();
            
            setupComplete = true;
            
            Debug.Log("🎉 COMPLETE GAME CREATED SUCCESSFULLY!");
            Debug.Log("✅ Player with movement and shooting");
            Debug.Log("✅ Complete UI system with HUD");
            Debug.Log("✅ AI enemies");
            Debug.Log("✅ Audio system");
            Debug.Log("✅ Game modes and scoring");
            Debug.Log("🎮 Press PLAY to test your game!");
            
            EditorUtility.DisplayDialog("Success!", 
                "Your complete Destiny-inspired PVP game is ready!\n\n" +
                "✅ Player movement and shooting\n" +
                "✅ Complete UI and HUD\n" +
                "✅ AI enemies\n" +
                "✅ Audio system\n" +
                "✅ Multiple game modes\n\n" +
                "Press PLAY to start gaming!", "Awesome!");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Setup failed: {e.Message}");
            EditorUtility.DisplayDialog("Setup Error", $"Something went wrong: {e.Message}", "OK");
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }
    }

    void CreateScene()
    {
        // Create terrain
        TerrainData terrainData = new TerrainData();
        terrainData.heightmapResolution = 513;
        terrainData.size = new Vector3(100, 10, 100);
        
        GameObject terrainObject = Terrain.CreateTerrainGameObject(terrainData);
        terrainObject.name = "Game Terrain";

        // Create lighting
        GameObject light = new GameObject("Directional Light");
        Light lightComponent = light.AddComponent<Light>();
        lightComponent.type = LightType.Directional;
        lightComponent.intensity = 1.5f;
        light.transform.rotation = Quaternion.Euler(50, -30, 0);

        // Create spawn points
        CreateSpawnPoints();
    }

    void CreateSpawnPoints()
    {
        GameObject spawnParent = new GameObject("Spawn Points");
        
        // Team A spawns
        GameObject teamAParent = new GameObject("Team A Spawns");
        teamAParent.transform.parent = spawnParent.transform;
        
        for (int i = 0; i < 4; i++)
        {
            GameObject spawn = new GameObject($"Team A Spawn {i + 1}");
            spawn.transform.parent = teamAParent.transform;
            spawn.transform.position = new Vector3(-20 + i * 5, 1, -10);
        }
        
        // Team B spawns
        GameObject teamBParent = new GameObject("Team B Spawns");
        teamBParent.transform.parent = spawnParent.transform;
        
        for (int i = 0; i < 4; i++)
        {
            GameObject spawn = new GameObject($"Team B Spawn {i + 1}");
            spawn.transform.parent = teamBParent.transform;
            spawn.transform.position = new Vector3(-20 + i * 5, 1, 10);
        }
    }

    GameObject CreatePlayer()
    {
        GameObject player;
        
        if (playerPrefab != null)
        {
            // Use custom player model
            player = Instantiate(playerPrefab);
            player.name = "Player";
        }
        else
        {
            // Create default player
            player = new GameObject("Player");
        }
        
        // Ensure player has required components
        if (player.GetComponent<CharacterController>() == null)
        {
            CharacterController controller = player.AddComponent<CharacterController>();
            controller.height = 2f;
            controller.radius = 0.5f;
        }
        
        // Add player scripts
        if (player.GetComponent<PlayerController>() == null)
            player.AddComponent<PlayerController>();
        if (player.GetComponent<PlayerHealth>() == null)
            player.AddComponent<PlayerHealth>();
        
        // Create camera
        GameObject cameraObj = new GameObject("Player Camera");
        cameraObj.transform.parent = player.transform;
        cameraObj.transform.localPosition = new Vector3(0, 1.6f, 0);
        Camera camera = cameraObj.AddComponent<Camera>();
        camera.tag = "MainCamera";
        
        // Create ground check
        GameObject groundCheck = new GameObject("Ground Check");
        groundCheck.transform.parent = player.transform;
        groundCheck.transform.localPosition = new Vector3(0, -1f, 0);
        
        // Assign references
        PlayerController playerController = player.GetComponent<PlayerController>();
        playerController.playerCamera = camera;
        playerController.groundCheck = groundCheck.transform;
        
        player.transform.position = new Vector3(0, 2, 0);
        player.tag = "Player";
        
        return player;
    }

    void CreateWeaponSystem(GameObject player)
    {
        // Create weapon holder
        GameObject weaponHolder = new GameObject("Weapon Holder");
        weaponHolder.transform.parent = player.transform;
        weaponHolder.transform.localPosition = new Vector3(0.5f, 1.2f, 0.5f);
        
        GameObject weapon;
        if (weaponPrefab != null)
        {
            // Use custom weapon model
            weapon = Instantiate(weaponPrefab, weaponHolder.transform);
            weapon.name = "Custom Weapon";
        }
        else
        {
            // Create default weapon
            weapon = GameObject.CreatePrimitive(PrimitiveType.Cube);
            weapon.name = "Default Weapon";
            weapon.transform.parent = weaponHolder.transform;
            weapon.transform.localPosition = Vector3.zero;
            weapon.transform.localScale = new Vector3(0.1f, 0.1f, 0.5f);
        }
        
        // Add weapon controller
        WeaponController weaponController = weaponHolder.AddComponent<WeaponController>();
        
        // Create fire point
        GameObject firePoint = new GameObject("Fire Point");
        firePoint.transform.parent = weapon.transform;
        firePoint.transform.localPosition = new Vector3(0, 0, 0.5f);
        weaponController.firePoint = firePoint.transform;
        weaponController.playerCamera = player.GetComponentInChildren<Camera>();
        
        // Create weapon data
        CreateWeaponData(weaponController);
    }

    void CreateWeaponData(WeaponController weaponController)
    {
        WeaponData weaponData = ScriptableObject.CreateInstance<WeaponData>();
        weaponData.weaponName = "Auto Rifle";
        weaponData.weaponType = WeaponType.AutoRifle;
        weaponData.damage = 25f;
        weaponData.fireRate = 600f;
        weaponData.magazineSize = 30;
        weaponData.maxReserveAmmo = 180;
        weaponData.reloadTime = 2.5f;
        weaponData.range = 100f;
        weaponData.isAutomatic = true;
        weaponData.recoil = 1f;
        weaponData.spread = 0.1f;
        weaponData.headShotMultiplier = 2f;
        
        // Assign sounds if provided
        if (weaponSounds != null && weaponSounds.Length > 0)
        {
            if (weaponSounds[0] != null) weaponData.fireSound = weaponSounds[0];
            if (weaponSounds[1] != null) weaponData.reloadSound = weaponSounds[1];
            if (weaponSounds[2] != null) weaponData.emptySound = weaponSounds[2];
        }
        
        // Save as asset
        string path = "Assets/WeaponData_AutoRifle.asset";
        AssetDatabase.CreateAsset(weaponData, path);
        AssetDatabase.SaveAssets();
        
        weaponController.weaponData = weaponData;
    }

    void CreateCompleteUI()
    {
        // Create main canvas
        GameObject canvas = new GameObject("Game Canvas");
        Canvas canvasComponent = canvas.AddComponent<Canvas>();
        canvasComponent.renderMode = RenderMode.ScreenSpaceOverlay;
        canvasComponent.sortingOrder = 0;

        CanvasScaler scaler = canvas.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        scaler.matchWidthOrHeight = 0.5f;

        canvas.AddComponent<GraphicRaycaster>();

        // Create HUD Manager
        GameObject hudManager = new GameObject("HUD Manager");
        hudManager.transform.SetParent(canvas.transform, false);
        HUDManager hudComponent = hudManager.AddComponent<HUDManager>();

        // Create health bar
        GameObject healthBar = CreateUISlider("Health Bar", canvas.transform, new Vector2(-200, -50), new Vector2(200, 20));
        Slider healthSlider = healthBar.GetComponent<Slider>();
        healthSlider.value = 1f;
        if (teamMaterials != null && teamMaterials[0] != null)
        {
            healthBar.transform.Find("Fill Area/Fill").GetComponent<Image>().color = Color.red;
        }

        // Create shield bar
        GameObject shieldBar = CreateUISlider("Shield Bar", canvas.transform, new Vector2(-200, -25), new Vector2(200, 20));
        Slider shieldSlider = shieldBar.GetComponent<Slider>();
        shieldSlider.value = 1f;
        shieldBar.transform.Find("Fill Area/Fill").GetComponent<Image>().color = Color.cyan;

        // Create ammo text
        GameObject ammoText = CreateUIText("Ammo Text", canvas.transform, new Vector2(200, -50), "30 / 180");

        // Create crosshair
        GameObject crosshair = CreateUIImage("Crosshair", canvas.transform, Vector2.zero, new Vector2(32, 32));
        if (uiTextures != null && uiTextures[0] != null)
        {
            crosshair.GetComponent<Image>().sprite = Sprite.Create(uiTextures[0],
                new Rect(0, 0, uiTextures[0].width, uiTextures[0].height), Vector2.one * 0.5f);
        }
        else
        {
            crosshair.GetComponent<Image>().color = Color.white;
        }

        // Create score text
        GameObject scoreText = CreateUIText("Score Text", canvas.transform, new Vector2(0, 100), "Team A: 0 | Team B: 0");

        // Create timer text
        GameObject timerText = CreateUIText("Timer Text", canvas.transform, new Vector2(0, 75), "10:00");

        // Create scoreboard
        CreateScoreboard(canvas);

        // Assign UI elements to HUD Manager
        hudComponent.healthBar = healthSlider;
        hudComponent.shieldBar = shieldSlider;
        hudComponent.ammoText = ammoText.GetComponent<TextMeshProUGUI>();
        hudComponent.crosshair = crosshair.GetComponent<Image>();
        hudComponent.scoreText = scoreText.GetComponent<TextMeshProUGUI>();
        hudComponent.timerText = timerText.GetComponent<TextMeshProUGUI>();
    }

    GameObject CreateUISlider(string name, Transform parent, Vector2 position, Vector2 size)
    {
        GameObject slider = new GameObject(name);
        slider.transform.SetParent(parent, false);

        RectTransform rect = slider.AddComponent<RectTransform>();
        rect.anchoredPosition = position;
        rect.sizeDelta = size;

        Slider sliderComponent = slider.AddComponent<Slider>();

        // Background
        GameObject background = new GameObject("Background");
        background.transform.SetParent(slider.transform, false);
        Image bgImage = background.AddComponent<Image>();
        bgImage.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        RectTransform bgRect = background.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.offsetMin = Vector2.zero;
        bgRect.offsetMax = Vector2.zero;

        // Fill Area
        GameObject fillArea = new GameObject("Fill Area");
        fillArea.transform.SetParent(slider.transform, false);
        RectTransform fillAreaRect = fillArea.GetComponent<RectTransform>();
        fillAreaRect.anchorMin = Vector2.zero;
        fillAreaRect.anchorMax = Vector2.one;
        fillAreaRect.offsetMin = Vector2.zero;
        fillAreaRect.offsetMax = Vector2.zero;

        // Fill
        GameObject fill = new GameObject("Fill");
        fill.transform.SetParent(fillArea.transform, false);
        Image fillImage = fill.AddComponent<Image>();
        fillImage.color = Color.green;
        RectTransform fillRect = fill.GetComponent<RectTransform>();
        fillRect.anchorMin = Vector2.zero;
        fillRect.anchorMax = Vector2.one;
        fillRect.offsetMin = Vector2.zero;
        fillRect.offsetMax = Vector2.zero;

        sliderComponent.fillRect = fillRect;
        sliderComponent.value = 1f;

        return slider;
    }

    GameObject CreateUIText(string name, Transform parent, Vector2 position, string text)
    {
        GameObject textObj = new GameObject(name);
        textObj.transform.SetParent(parent, false);

        RectTransform rect = textObj.AddComponent<RectTransform>();
        rect.anchoredPosition = position;
        rect.sizeDelta = new Vector2(200, 30);

        TextMeshProUGUI textComponent = textObj.AddComponent<TextMeshProUGUI>();
        textComponent.text = text;
        textComponent.fontSize = 18;
        textComponent.color = Color.white;
        textComponent.alignment = TextAlignmentOptions.Center;

        return textObj;
    }

    GameObject CreateUIImage(string name, Transform parent, Vector2 position, Vector2 size)
    {
        GameObject imageObj = new GameObject(name);
        imageObj.transform.SetParent(parent, false);

        RectTransform rect = imageObj.AddComponent<RectTransform>();
        rect.anchoredPosition = position;
        rect.sizeDelta = size;

        Image image = imageObj.AddComponent<Image>();
        image.color = Color.white;

        return imageObj;
    }

    void CreateScoreboard(GameObject canvas)
    {
        GameObject scoreboardPanel = new GameObject("Scoreboard Panel");
        scoreboardPanel.transform.SetParent(canvas.transform, false);
        scoreboardPanel.SetActive(false);

        RectTransform rect = scoreboardPanel.AddComponent<RectTransform>();
        rect.anchorMin = Vector2.zero;
        rect.anchorMax = Vector2.one;
        rect.offsetMin = Vector2.zero;
        rect.offsetMax = Vector2.zero;

        Image background = scoreboardPanel.AddComponent<Image>();
        background.color = new Color(0, 0, 0, 0.8f);

        ScoreboardManager scoreboardManager = scoreboardPanel.AddComponent<ScoreboardManager>();
        scoreboardManager.scoreboardPanel = scoreboardPanel;
    }

    void CreateEnemies()
    {
        GameObject enemiesParent = new GameObject("AI Enemies");

        for (int i = 0; i < 3; i++)
        {
            GameObject enemy;

            if (enemyPrefab != null)
            {
                enemy = Instantiate(enemyPrefab);
                enemy.name = $"AI Enemy {i + 1}";
            }
            else
            {
                enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                enemy.name = $"AI Enemy {i + 1}";
            }

            enemy.transform.parent = enemiesParent.transform;
            enemy.transform.position = new Vector3(10 + i * 5, 1, 10);
            enemy.tag = "Enemy";

            // Add AI components
            if (enemy.GetComponent<NavMeshAgent>() == null)
                enemy.AddComponent<NavMeshAgent>();
            if (enemy.GetComponent<SimpleAI>() == null)
                enemy.AddComponent<SimpleAI>();
            if (enemy.GetComponent<PlayerHealth>() == null)
                enemy.AddComponent<PlayerHealth>();

            // Create fire point for AI
            GameObject firePoint = new GameObject("Fire Point");
            firePoint.transform.parent = enemy.transform;
            firePoint.transform.localPosition = new Vector3(0, 1, 0.5f);

            SimpleAI ai = enemy.GetComponent<SimpleAI>();
            ai.firePoint = firePoint.transform;
        }
    }

    void CreateAudioSystem()
    {
        GameObject audioManager = new GameObject("Audio Manager");
        audioManager.AddComponent<AudioManager>();

        // Add background music source
        AudioSource musicSource = audioManager.AddComponent<AudioSource>();
        musicSource.loop = true;
        musicSource.volume = 0.5f;
        musicSource.playOnAwake = false;
    }

    void CreateGameManager()
    {
        GameObject gameManager = new GameObject("Game Manager");
        GameManager gmComponent = gameManager.AddComponent<GameManager>();

        // Find and assign spawn points
        GameObject spawnPoints = GameObject.Find("Spawn Points");
        if (spawnPoints != null)
        {
            Transform teamASpawns = spawnPoints.transform.Find("Team A Spawns");
            Transform teamBSpawns = spawnPoints.transform.Find("Team B Spawns");

            if (teamASpawns != null)
            {
                Transform[] teamAArray = new Transform[teamASpawns.childCount];
                for (int i = 0; i < teamASpawns.childCount; i++)
                {
                    teamAArray[i] = teamASpawns.GetChild(i);
                }
                gmComponent.teamASpawns = teamAArray;
            }

            if (teamBSpawns != null)
            {
                Transform[] teamBArray = new Transform[teamBSpawns.childCount];
                for (int i = 0; i < teamBSpawns.childCount; i++)
                {
                    teamBArray[i] = teamBSpawns.GetChild(i);
                }
                gmComponent.teamBSpawns = teamBArray;
            }
        }

        // Find and assign UI references
        gmComponent.hudManager = FindObjectOfType<HUDManager>();
        gmComponent.scoreboardManager = FindObjectOfType<ScoreboardManager>();
    }

    void FinalizeSetup()
    {
        // Set up layers
        SetupLayers();

        // Create NavMesh
        CreateNavMesh();

        // Set camera settings
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            mainCamera.fieldOfView = 75f;
            mainCamera.nearClipPlane = 0.1f;
            mainCamera.farClipPlane = 1000f;
        }

        // Save scene
        UnityEditor.SceneManagement.EditorSceneManager.SaveOpenScenes();

        Debug.Log("🎮 Game setup finalized!");
    }

    void SetupLayers()
    {
        // This would set up custom layers for players, enemies, etc.
        // For simplicity, we'll use default layers
    }

    void CreateNavMesh()
    {
        // Auto-bake NavMesh
        GameObject terrain = GameObject.Find("Game Terrain");
        if (terrain != null)
        {
            GameObjectUtility.SetStaticEditorFlags(terrain, StaticEditorFlags.NavigationStatic);
        }

        // Bake NavMesh
        NavMeshBuilder.BuildNavMesh();
    }
}

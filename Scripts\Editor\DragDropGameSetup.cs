using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using TMPro;
using UnityEngine.AI;
using System.Collections.Generic;
using UnityEngine.Audio;

public class DragDropGameSetup : EditorWindow
{
    private bool setupComplete = false;
    private Vector2 scrollPosition;

    [MenuItem("Tools/Drag & Drop Game Setup")]
    public static void ShowWindow()
    {
        DragDropGameSetup window = GetWindow<DragDropGameSetup>("🚀 MASSIVE DESTINY GAME");
        window.minSize = new Vector2(500, 400);
    }

    void OnGUI()
    {
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        GUILayout.Label("🚀 MASSIVE DESTINY PVP GAME CREATOR", EditorStyles.boldLabel);
        GUILayout.Space(10);

        EditorGUILayout.HelpBox("🎮 CREATES A COMPLETE DESTINY-INSPIRED PVP GAME:\n\n" +
            "✅ 4 WEAPON TYPES: Auto Rifle, <PERSON> <PERSON>, <PERSON><PERSON><PERSON>, Rocket Launcher\n" +
            "✅ ADVANCED MOVEMENT: Sprint, Slide, Jump, Crouch, Wall-running\n" +
            "✅ COMPLETE UI: Health, Shield, Ammo, Radar, Scoreboard\n" +
            "✅ MULTIPLE ENEMY TYPES: Grunts, Elites, Bosses with unique AI\n" +
            "✅ POWER-UPS: Damage boost, Speed boost, Shield recharge\n" +
            "✅ TEAM GAMEPLAY: 2 teams with spawn points and objectives\n" +
            "✅ SOUND SYSTEM: Weapon sounds, ambient music, hit markers\n" +
            "✅ VISUAL EFFECTS: Muzzle flashes, explosions, hit effects\n" +
            "✅ GAME MODES: Team Deathmatch, Capture the Flag, Control Points\n" +
            "✅ MASSIVE MAPS: Multiple environments with cover and verticality", MessageType.Info);

        GUILayout.Space(10);

        GUI.backgroundColor = Color.red;
        if (GUILayout.Button("🎮 CREATE MASSIVE COMPLETE GAME", GUILayout.Height(60)))
        {
            CreateMassiveGame();
        }
        GUI.backgroundColor = Color.white;

        if (setupComplete)
        {
            EditorGUILayout.HelpBox("✅ MASSIVE GAME CREATED! Press Play to experience the full game!", MessageType.Info);

            if (GUILayout.Button("🎯 START PLAYING NOW!", GUILayout.Height(40)))
            {
                EditorApplication.isPlaying = true;
            }
        }

        EditorGUILayout.EndScrollView();
    }

    void CreateMassiveGame()
    {
        try
        {
            EditorUtility.DisplayProgressBar("Creating MASSIVE Game", "Building massive world...", 0.1f);
            CreateMassiveWorld();

            EditorUtility.DisplayProgressBar("Creating MASSIVE Game", "Creating advanced player...", 0.2f);
            GameObject player = CreateAdvancedPlayer();

            EditorUtility.DisplayProgressBar("Creating MASSIVE Game", "Building weapon arsenal...", 0.3f);
            CreateWeaponArsenal(player);

            EditorUtility.DisplayProgressBar("Creating MASSIVE Game", "Creating enemy army...", 0.4f);
            CreateEnemyArmy();

            EditorUtility.DisplayProgressBar("Creating MASSIVE Game", "Building complete UI...", 0.5f);
            CreateCompleteUI();

            EditorUtility.DisplayProgressBar("Creating MASSIVE Game", "Adding power-ups...", 0.6f);
            CreatePowerUps();

            EditorUtility.DisplayProgressBar("Creating MASSIVE Game", "Setting up game modes...", 0.7f);
            CreateGameModes();

            EditorUtility.DisplayProgressBar("Creating MASSIVE Game", "Adding visual effects...", 0.8f);
            CreateVisualEffects();

            EditorUtility.DisplayProgressBar("Creating MASSIVE Game", "Finalizing systems...", 0.9f);
            CreateGameSystems();

            setupComplete = true;

            Debug.Log("🎉 MASSIVE DESTINY PVP GAME CREATED SUCCESSFULLY!");
            Debug.Log("🎮 Features included: Multiple weapons, advanced movement, complete UI, AI enemies, power-ups, and more!");

            EditorUtility.DisplayDialog("MASSIVE SUCCESS!",
                "Your complete Destiny-inspired PVP game is ready!\n\n" +
                "Features:\n" +
                "✅ 4 Weapon Types\n" +
                "✅ Advanced Movement\n" +
                "✅ Complete UI System\n" +
                "✅ Multiple Enemy Types\n" +
                "✅ Power-ups & Abilities\n" +
                "✅ Team-based Gameplay\n" +
                "✅ Sound & Visual Effects\n\n" +
                "Press PLAY to start your epic PVP experience!", "LET'S PLAY!");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Setup failed: {e.Message}");
            EditorUtility.DisplayDialog("Setup Error", $"Something went wrong: {e.Message}", "OK");
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }
    }

    void CreateMassiveWorld()
    {
        // Create multiple terrains for massive world
        CreateMultipleTerrains();

        // Create advanced lighting system
        CreateAdvancedLighting();

        // Create massive structures and cover
        CreateStructures();

        // Create spawn points for teams
        CreateTeamSpawnPoints();

        // Create objective points
        CreateObjectivePoints();
    }

    void CreateMultipleTerrains()
    {
        GameObject terrainParent = new GameObject("Massive World Terrains");

        for (int x = 0; x < 3; x++)
        {
            for (int z = 0; z < 3; z++)
            {
                TerrainData terrainData = new TerrainData();
                terrainData.heightmapResolution = 513;
                terrainData.size = new Vector3(100, 20, 100);

                GameObject terrainObject = Terrain.CreateTerrainGameObject(terrainData);
                terrainObject.name = $"Terrain_{x}_{z}";
                terrainObject.transform.parent = terrainParent.transform;
                terrainObject.transform.position = new Vector3(x * 100, 0, z * 100);

                // Add random height variations
                Terrain terrain = terrainObject.GetComponent<Terrain>();
                float[,] heights = new float[513, 513];
                for (int i = 0; i < 513; i++)
                {
                    for (int j = 0; j < 513; j++)
                    {
                        heights[i, j] = Mathf.PerlinNoise(i * 0.01f, j * 0.01f) * 0.1f;
                    }
                }
                terrainData.SetHeights(0, 0, heights);
            }
        }
    }

    void CreateAdvancedLighting()
    {
        // Main directional light (sun)
        GameObject sunLight = new GameObject("Sun Light");
        Light sun = sunLight.AddComponent<Light>();
        sun.type = LightType.Directional;
        sun.intensity = 2f;
        sun.color = new Color(1f, 0.95f, 0.8f);
        sun.shadows = LightShadows.Soft;
        sunLight.transform.rotation = Quaternion.Euler(45, -30, 0);

        // Ambient lighting
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = new Color(0.5f, 0.7f, 1f);
        RenderSettings.ambientEquatorColor = new Color(0.4f, 0.4f, 0.4f);
        RenderSettings.ambientGroundColor = new Color(0.2f, 0.2f, 0.2f);

        // Fog for atmosphere
        RenderSettings.fog = true;
        RenderSettings.fogColor = new Color(0.7f, 0.8f, 1f);
        RenderSettings.fogMode = FogMode.ExponentialSquared;
        RenderSettings.fogDensity = 0.01f;
    }

    void CreateStructures()
    {
        GameObject structuresParent = new GameObject("Map Structures");

        // Create buildings and cover
        for (int i = 0; i < 20; i++)
        {
            GameObject structure = GameObject.CreatePrimitive(PrimitiveType.Cube);
            structure.name = $"Cover_{i}";
            structure.transform.parent = structuresParent.transform;
            structure.transform.position = new Vector3(
                Random.Range(-50, 50),
                Random.Range(2, 8),
                Random.Range(-50, 50)
            );
            structure.transform.localScale = new Vector3(
                Random.Range(2, 6),
                Random.Range(3, 10),
                Random.Range(2, 6)
            );
            structure.GetComponent<Renderer>().material.color = new Color(0.6f, 0.6f, 0.6f);
        }

        // Create towers for verticality
        for (int i = 0; i < 5; i++)
        {
            GameObject tower = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            tower.name = $"Tower_{i}";
            tower.transform.parent = structuresParent.transform;
            tower.transform.position = new Vector3(
                Random.Range(-40, 40),
                10,
                Random.Range(-40, 40)
            );
            tower.transform.localScale = new Vector3(3, 20, 3);
            tower.GetComponent<Renderer>().material.color = new Color(0.4f, 0.4f, 0.4f);
        }
    }

    void CreateTeamSpawnPoints()
    {
        GameObject spawnParent = new GameObject("Team Spawn Points");

        // Team A spawns (Red team)
        GameObject teamAParent = new GameObject("Team A Spawns");
        teamAParent.transform.parent = spawnParent.transform;

        for (int i = 0; i < 8; i++)
        {
            GameObject spawn = new GameObject($"Team A Spawn {i + 1}");
            spawn.transform.parent = teamAParent.transform;
            spawn.transform.position = new Vector3(-40 + i * 3, 2, -40);
            spawn.tag = "TeamASpawn";
        }

        // Team B spawns (Blue team)
        GameObject teamBParent = new GameObject("Team B Spawns");
        teamBParent.transform.parent = spawnParent.transform;

        for (int i = 0; i < 8; i++)
        {
            GameObject spawn = new GameObject($"Team B Spawn {i + 1}");
            spawn.transform.parent = teamBParent.transform;
            spawn.transform.position = new Vector3(-40 + i * 3, 2, 40);
            spawn.tag = "TeamBSpawn";
        }
    }

    void CreateObjectivePoints()
    {
        GameObject objectivesParent = new GameObject("Objective Points");

        // Control points for domination mode
        for (int i = 0; i < 3; i++)
        {
            GameObject controlPoint = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            controlPoint.name = $"Control Point {(char)('A' + i)}";
            controlPoint.transform.parent = objectivesParent.transform;
            controlPoint.transform.position = new Vector3(
                i == 1 ? 0 : (i == 0 ? -25 : 25),
                1,
                0
            );
            controlPoint.transform.localScale = new Vector3(4, 0.5f, 4);
            controlPoint.GetComponent<Renderer>().material.color = Color.yellow;
            controlPoint.tag = "ControlPoint";
        }

        // Flag bases for CTF mode
        GameObject flagA = GameObject.CreatePrimitive(PrimitiveType.Cube);
        flagA.name = "Flag Base A";
        flagA.transform.parent = objectivesParent.transform;
        flagA.transform.position = new Vector3(-45, 3, 0);
        flagA.transform.localScale = new Vector3(2, 6, 2);
        flagA.GetComponent<Renderer>().material.color = Color.red;
        flagA.tag = "FlagA";

        GameObject flagB = GameObject.CreatePrimitive(PrimitiveType.Cube);
        flagB.name = "Flag Base B";
        flagB.transform.parent = objectivesParent.transform;
        flagB.transform.position = new Vector3(45, 3, 0);
        flagB.transform.localScale = new Vector3(2, 6, 2);
        flagB.GetComponent<Renderer>().material.color = Color.blue;
        flagB.tag = "FlagB";
    }

    GameObject CreateAdvancedPlayer()
    {
        GameObject player = new GameObject("Advanced Player");

        // Add CharacterController with advanced settings
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.height = 2f;
        controller.radius = 0.5f;
        controller.stepOffset = 0.3f;
        controller.slopeLimit = 45f;

        // Add advanced player controller
        player.AddComponent<AdvancedPlayerController>();
        player.AddComponent<PlayerHealth>();
        player.AddComponent<PlayerAbilities>();
        player.AddComponent<PlayerStats>();

        // Create camera system
        GameObject cameraRig = new GameObject("Camera Rig");
        cameraRig.transform.parent = player.transform;
        cameraRig.transform.localPosition = new Vector3(0, 1.6f, 0);

        GameObject cameraObj = new GameObject("Player Camera");
        cameraObj.transform.parent = cameraRig.transform;
        cameraObj.transform.localPosition = Vector3.zero;
        Camera camera = cameraObj.AddComponent<Camera>();
        camera.tag = "MainCamera";
        camera.fieldOfView = 75f;
        camera.nearClipPlane = 0.1f;
        camera.farClipPlane = 1000f;

        // Add camera effects
        cameraObj.AddComponent<CameraController>();

        // Create ground check system
        GameObject groundCheck = new GameObject("Ground Check");
        groundCheck.transform.parent = player.transform;
        groundCheck.transform.localPosition = new Vector3(0, -1f, 0);

        // Create wall check for wall-running
        GameObject wallCheckLeft = new GameObject("Wall Check Left");
        wallCheckLeft.transform.parent = player.transform;
        wallCheckLeft.transform.localPosition = new Vector3(-0.6f, 0, 0);

        GameObject wallCheckRight = new GameObject("Wall Check Right");
        wallCheckRight.transform.parent = player.transform;
        wallCheckRight.transform.localPosition = new Vector3(0.6f, 0, 0);

        // Create weapon holder
        GameObject weaponHolder = new GameObject("Weapon Holder");
        weaponHolder.transform.parent = cameraRig.transform;
        weaponHolder.transform.localPosition = new Vector3(0.3f, -0.3f, 0.5f);

        player.transform.position = new Vector3(0, 5, 0);
        player.tag = "Player";

        return player;
    }

    void CreateWeaponArsenal(GameObject player)
    {
        GameObject weaponHolder = player.transform.Find("Camera Rig/Weapon Holder").gameObject;

        // Add weapon manager
        WeaponManager weaponManager = weaponHolder.AddComponent<WeaponManager>();

        // Create 4 different weapon types
        CreateAutoRifle(weaponHolder);
        CreateHandCannon(weaponHolder);
        CreateSniperRifle(weaponHolder);
        CreateRocketLauncher(weaponHolder);

        // Set up weapon switching
        weaponManager.weapons = weaponHolder.GetComponentsInChildren<WeaponController>();
        weaponManager.currentWeaponIndex = 0;

        // Only show first weapon initially
        for (int i = 1; i < weaponManager.weapons.Length; i++)
        {
            weaponManager.weapons[i].gameObject.SetActive(false);
        }
    }

    void CreateAutoRifle(GameObject weaponHolder)
    {
        GameObject autoRifle = GameObject.CreatePrimitive(PrimitiveType.Cube);
        autoRifle.name = "Auto Rifle";
        autoRifle.transform.parent = weaponHolder.transform;
        autoRifle.transform.localPosition = Vector3.zero;
        autoRifle.transform.localScale = new Vector3(0.1f, 0.1f, 0.6f);
        autoRifle.GetComponent<Renderer>().material.color = Color.black;

        WeaponController weaponController = autoRifle.AddComponent<WeaponController>();

        GameObject firePoint = new GameObject("Fire Point");
        firePoint.transform.parent = autoRifle.transform;
        firePoint.transform.localPosition = new Vector3(0, 0, 0.5f);
        weaponController.firePoint = firePoint.transform;

        // Create weapon data
        WeaponData weaponData = ScriptableObject.CreateInstance<WeaponData>();
        weaponData.weaponName = "Auto Rifle";
        weaponData.weaponType = WeaponType.AutoRifle;
        weaponData.damage = 25f;
        weaponData.fireRate = 600f;
        weaponData.magazineSize = 30;
        weaponData.maxReserveAmmo = 180;
        weaponData.reloadTime = 2.5f;
        weaponData.range = 100f;
        weaponData.isAutomatic = true;
        weaponData.recoil = 1f;
        weaponData.spread = 0.1f;
        weaponData.headShotMultiplier = 2f;

        string path = "Assets/WeaponData_AutoRifle.asset";
        AssetDatabase.CreateAsset(weaponData, path);
        AssetDatabase.SaveAssets();
        weaponController.weaponData = weaponData;
    }

    void CreateHandCannon(GameObject weaponHolder)
    {
        GameObject handCannon = GameObject.CreatePrimitive(PrimitiveType.Cube);
        handCannon.name = "Hand Cannon";
        handCannon.transform.parent = weaponHolder.transform;
        handCannon.transform.localPosition = Vector3.zero;
        handCannon.transform.localScale = new Vector3(0.15f, 0.1f, 0.4f);
        handCannon.GetComponent<Renderer>().material.color = Color.gray;

        WeaponController weaponController = handCannon.AddComponent<WeaponController>();

        GameObject firePoint = new GameObject("Fire Point");
        firePoint.transform.parent = handCannon.transform;
        firePoint.transform.localPosition = new Vector3(0, 0, 0.4f);
        weaponController.firePoint = firePoint.transform;

        WeaponData weaponData = ScriptableObject.CreateInstance<WeaponData>();
        weaponData.weaponName = "Hand Cannon";
        weaponData.weaponType = WeaponType.HandCannon;
        weaponData.damage = 75f;
        weaponData.fireRate = 180f;
        weaponData.magazineSize = 12;
        weaponData.maxReserveAmmo = 72;
        weaponData.reloadTime = 2.8f;
        weaponData.range = 80f;
        weaponData.isAutomatic = false;
        weaponData.recoil = 3f;
        weaponData.spread = 0.05f;
        weaponData.headShotMultiplier = 3f;

        string path = "Assets/WeaponData_HandCannon.asset";
        AssetDatabase.CreateAsset(weaponData, path);
        AssetDatabase.SaveAssets();
        weaponController.weaponData = weaponData;
    }

    void CreateSniperRifle(GameObject weaponHolder)
    {
        GameObject sniper = GameObject.CreatePrimitive(PrimitiveType.Cube);
        sniper.name = "Sniper Rifle";
        sniper.transform.parent = weaponHolder.transform;
        sniper.transform.localPosition = Vector3.zero;
        sniper.transform.localScale = new Vector3(0.08f, 0.08f, 1.2f);
        sniper.GetComponent<Renderer>().material.color = Color.green;

        WeaponController weaponController = sniper.AddComponent<WeaponController>();

        GameObject firePoint = new GameObject("Fire Point");
        firePoint.transform.parent = sniper.transform;
        firePoint.transform.localPosition = new Vector3(0, 0, 0.6f);
        weaponController.firePoint = firePoint.transform;

        WeaponData weaponData = ScriptableObject.CreateInstance<WeaponData>();
        weaponData.weaponName = "Sniper Rifle";
        weaponData.weaponType = WeaponType.SniperRifle;
        weaponData.damage = 150f;
        weaponData.fireRate = 60f;
        weaponData.magazineSize = 5;
        weaponData.maxReserveAmmo = 25;
        weaponData.reloadTime = 3.5f;
        weaponData.range = 300f;
        weaponData.isAutomatic = false;
        weaponData.recoil = 5f;
        weaponData.spread = 0.01f;
        weaponData.headShotMultiplier = 5f;

        string path = "Assets/WeaponData_SniperRifle.asset";
        AssetDatabase.CreateAsset(weaponData, path);
        AssetDatabase.SaveAssets();
        weaponController.weaponData = weaponData;
    }

    void CreateRocketLauncher(GameObject weaponHolder)
    {
        GameObject rocket = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        rocket.name = "Rocket Launcher";
        rocket.transform.parent = weaponHolder.transform;
        rocket.transform.localPosition = Vector3.zero;
        rocket.transform.localScale = new Vector3(0.2f, 0.4f, 0.2f);
        rocket.transform.localRotation = Quaternion.Euler(90, 0, 0);
        rocket.GetComponent<Renderer>().material.color = Color.red;

        WeaponController weaponController = rocket.AddComponent<WeaponController>();

        GameObject firePoint = new GameObject("Fire Point");
        firePoint.transform.parent = rocket.transform;
        firePoint.transform.localPosition = new Vector3(0, 0.4f, 0);
        weaponController.firePoint = firePoint.transform;

        WeaponData weaponData = ScriptableObject.CreateInstance<WeaponData>();
        weaponData.weaponName = "Rocket Launcher";
        weaponData.weaponType = WeaponType.RocketLauncher;
        weaponData.damage = 200f;
        weaponData.fireRate = 30f;
        weaponData.magazineSize = 1;
        weaponData.maxReserveAmmo = 8;
        weaponData.reloadTime = 4f;
        weaponData.range = 150f;
        weaponData.isAutomatic = false;
        weaponData.recoil = 8f;
        weaponData.spread = 0.2f;
        weaponData.headShotMultiplier = 1f;
        weaponData.explosionRadius = 5f;

        string path = "Assets/WeaponData_RocketLauncher.asset";
        AssetDatabase.CreateAsset(weaponData, path);
        AssetDatabase.SaveAssets();
        weaponController.weaponData = weaponData;
    }

    void CreateEnemyArmy()
    {
        GameObject enemiesParent = new GameObject("Enemy Army");

        // Create different enemy types
        CreateGruntEnemies(enemiesParent);
        CreateEliteEnemies(enemiesParent);
        CreateBossEnemies(enemiesParent);
    }

    void CreateGruntEnemies(GameObject parent)
    {
        GameObject gruntParent = new GameObject("Grunt Enemies");
        gruntParent.transform.parent = parent.transform;

        for (int i = 0; i < 10; i++)
        {
            GameObject grunt = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            grunt.name = $"Grunt_{i}";
            grunt.transform.parent = gruntParent.transform;
            grunt.transform.position = new Vector3(
                Random.Range(-30, 30),
                1,
                Random.Range(10, 40)
            );
            grunt.transform.localScale = new Vector3(0.8f, 1f, 0.8f);
            grunt.GetComponent<Renderer>().material.color = Color.red;
            grunt.tag = "Enemy";

            // Add AI components
            grunt.AddComponent<NavMeshAgent>();
            GruntAI gruntAI = grunt.AddComponent<GruntAI>();
            grunt.AddComponent<EnemyHealth>();

            // Set grunt stats
            gruntAI.health = 50f;
            gruntAI.damage = 15f;
            gruntAI.moveSpeed = 3f;
            gruntAI.attackRange = 15f;
            gruntAI.detectionRange = 25f;
        }
    }

    void CreateEliteEnemies(GameObject parent)
    {
        GameObject eliteParent = new GameObject("Elite Enemies");
        eliteParent.transform.parent = parent.transform;

        for (int i = 0; i < 5; i++)
        {
            GameObject elite = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            elite.name = $"Elite_{i}";
            elite.transform.parent = eliteParent.transform;
            elite.transform.position = new Vector3(
                Random.Range(-25, 25),
                1,
                Random.Range(15, 35)
            );
            elite.transform.localScale = new Vector3(1.2f, 1.5f, 1.2f);
            elite.GetComponent<Renderer>().material.color = Color.yellow;
            elite.tag = "Enemy";

            // Add AI components
            elite.AddComponent<NavMeshAgent>();
            EliteAI eliteAI = elite.AddComponent<EliteAI>();
            elite.AddComponent<EnemyHealth>();

            // Set elite stats
            eliteAI.health = 150f;
            eliteAI.damage = 35f;
            eliteAI.moveSpeed = 4f;
            eliteAI.attackRange = 25f;
            eliteAI.detectionRange = 40f;
        }
    }

    void CreateBossEnemies(GameObject parent)
    {
        GameObject bossParent = new GameObject("Boss Enemies");
        bossParent.transform.parent = parent.transform;

        for (int i = 0; i < 2; i++)
        {
            GameObject boss = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            boss.name = $"Boss_{i}";
            boss.transform.parent = bossParent.transform;
            boss.transform.position = new Vector3(
                i == 0 ? -20 : 20,
                1,
                25
            );
            boss.transform.localScale = new Vector3(2f, 3f, 2f);
            boss.GetComponent<Renderer>().material.color = Color.magenta;
            boss.tag = "Boss";

            // Add AI components
            boss.AddComponent<NavMeshAgent>();
            BossAI bossAI = boss.AddComponent<BossAI>();
            boss.AddComponent<EnemyHealth>();

            // Set boss stats
            bossAI.health = 500f;
            bossAI.damage = 75f;
            bossAI.moveSpeed = 2f;
            bossAI.attackRange = 30f;
            bossAI.detectionRange = 50f;
        }
    }

    void CreateCompleteUI()
    {
        // Create main canvas
        GameObject canvas = new GameObject("Complete Game UI");
        Canvas canvasComponent = canvas.AddComponent<Canvas>();
        canvasComponent.renderMode = RenderMode.ScreenSpaceOverlay;
        canvasComponent.sortingOrder = 0;

        CanvasScaler scaler = canvas.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        scaler.matchWidthOrHeight = 0.5f;

        canvas.AddComponent<GraphicRaycaster>();

        // Create HUD Manager
        GameObject hudManager = new GameObject("Advanced HUD Manager");
        hudManager.transform.SetParent(canvas.transform, false);
        AdvancedHUDManager hudComponent = hudManager.AddComponent<AdvancedHUDManager>();

        // Create health and shield system
        CreateHealthShieldUI(canvas);

        // Create weapon and ammo UI
        CreateWeaponUI(canvas);

        // Create minimap/radar
        CreateRadarUI(canvas);

        // Create scoreboard and timer
        CreateScoreTimerUI(canvas);

        // Create ability UI
        CreateAbilityUI(canvas);

        // Create crosshair system
        CreateAdvancedCrosshair(canvas);

        // Create damage indicators
        CreateDamageIndicators(canvas);

        // Create kill feed
        CreateKillFeed(canvas);
    }

    void CreateHealthShieldUI(GameObject canvas)
    {
        // Health bar
        GameObject healthBar = CreateUISlider("Health Bar", canvas.transform, new Vector2(-300, -100), new Vector2(250, 25));
        healthBar.transform.Find("Fill Area/Fill").GetComponent<Image>().color = Color.red;

        // Shield bar
        GameObject shieldBar = CreateUISlider("Shield Bar", canvas.transform, new Vector2(-300, -70), new Vector2(250, 20));
        shieldBar.transform.Find("Fill Area/Fill").GetComponent<Image>().color = Color.cyan;

        // Health text
        CreateUIText("Health Text", canvas.transform, new Vector2(-300, -130), "100 HP");
    }

    void CreateWeaponUI(GameObject canvas)
    {
        // Current weapon display
        CreateUIText("Current Weapon", canvas.transform, new Vector2(300, -50), "Auto Rifle");

        // Ammo counter
        CreateUIText("Ammo Counter", canvas.transform, new Vector2(300, -80), "30 / 180");

        // Weapon wheel background
        GameObject weaponWheel = CreateUIImage("Weapon Wheel", canvas.transform, Vector2.zero, new Vector2(200, 200));
        weaponWheel.GetComponent<Image>().color = new Color(0, 0, 0, 0.5f);
        weaponWheel.SetActive(false);

        // Reload indicator
        GameObject reloadBar = CreateUISlider("Reload Bar", canvas.transform, new Vector2(300, -110), new Vector2(150, 15));
        reloadBar.transform.Find("Fill Area/Fill").GetComponent<Image>().color = Color.yellow;
        reloadBar.SetActive(false);
    }

    void CreateRadarUI(GameObject canvas)
    {
        GameObject radar = CreateUIImage("Radar", canvas.transform, new Vector2(-400, 300), new Vector2(150, 150));
        radar.GetComponent<Image>().color = new Color(0, 0.5f, 0, 0.8f);

        // Radar dots for enemies
        for (int i = 0; i < 5; i++)
        {
            GameObject dot = CreateUIImage($"Enemy Dot {i}", radar.transform,
                new Vector2(Random.Range(-60, 60), Random.Range(-60, 60)), new Vector2(8, 8));
            dot.GetComponent<Image>().color = Color.red;
        }

        // Player dot (center)
        GameObject playerDot = CreateUIImage("Player Dot", radar.transform, Vector2.zero, new Vector2(10, 10));
        playerDot.GetComponent<Image>().color = Color.blue;
    }

    void CreateScoreTimerUI(GameObject canvas)
    {
        // Team scores
        CreateUIText("Team A Score", canvas.transform, new Vector2(-100, 400), "Red Team: 0");
        CreateUIText("Team B Score", canvas.transform, new Vector2(100, 400), "Blue Team: 0");

        // Match timer
        CreateUIText("Match Timer", canvas.transform, new Vector2(0, 450), "10:00");

        // Game mode indicator
        CreateUIText("Game Mode", canvas.transform, new Vector2(0, 420), "Team Deathmatch");

        // Kill/Death counter
        CreateUIText("KD Counter", canvas.transform, new Vector2(0, -400), "K: 0 | D: 0");
    }

    void CreateAbilityUI(GameObject canvas)
    {
        // Ability cooldown indicators
        for (int i = 0; i < 3; i++)
        {
            GameObject abilitySlot = CreateUIImage($"Ability {i + 1}", canvas.transform,
                new Vector2(-400 + i * 80, -300), new Vector2(60, 60));
            abilitySlot.GetComponent<Image>().color = new Color(0.3f, 0.3f, 0.3f, 0.8f);

            // Cooldown overlay
            GameObject cooldown = CreateUIImage($"Cooldown {i + 1}", abilitySlot.transform,
                Vector2.zero, new Vector2(60, 60));
            cooldown.GetComponent<Image>().color = new Color(0, 0, 0, 0.7f);

            // Ability key text
            CreateUIText($"Key {i + 1}", abilitySlot.transform, new Vector2(0, -40), $"Q");
        }
    }

    void CreateAdvancedCrosshair(GameObject canvas)
    {
        GameObject crosshairParent = new GameObject("Advanced Crosshair");
        crosshairParent.transform.SetParent(canvas.transform, false);

        // Dynamic crosshair that changes based on weapon
        GameObject crosshair = CreateUIImage("Dynamic Crosshair", crosshairParent.transform, Vector2.zero, new Vector2(32, 32));
        crosshair.GetComponent<Image>().color = Color.white;

        // Hit marker
        GameObject hitMarker = CreateUIImage("Hit Marker", crosshairParent.transform, Vector2.zero, new Vector2(40, 40));
        hitMarker.GetComponent<Image>().color = Color.red;
        hitMarker.SetActive(false);

        // Damage numbers
        GameObject damageNumbers = CreateUIText("Damage Numbers", crosshairParent.transform, new Vector2(0, 30), "");
        damageNumbers.GetComponent<TextMeshProUGUI>().color = Color.yellow;
    }

    void CreateDamageIndicators(GameObject canvas)
    {
        GameObject damageParent = new GameObject("Damage Indicators");
        damageParent.transform.SetParent(canvas.transform, false);

        // Directional damage indicators
        for (int i = 0; i < 8; i++)
        {
            GameObject indicator = CreateUIImage($"Damage Indicator {i}", damageParent.transform,
                Vector2.zero, new Vector2(20, 60));
            indicator.GetComponent<Image>().color = Color.red;
            indicator.SetActive(false);

            // Position around screen edge
            float angle = i * 45f;
            Vector2 position = new Vector2(
                Mathf.Sin(angle * Mathf.Deg2Rad) * 300,
                Mathf.Cos(angle * Mathf.Deg2Rad) * 200
            );
            indicator.GetComponent<RectTransform>().anchoredPosition = position;
        }
    }

    void CreateKillFeed(GameObject canvas)
    {
        GameObject killFeedParent = new GameObject("Kill Feed");
        killFeedParent.transform.SetParent(canvas.transform, false);

        RectTransform rect = killFeedParent.AddComponent<RectTransform>();
        rect.anchorMin = new Vector2(1, 1);
        rect.anchorMax = new Vector2(1, 1);
        rect.anchoredPosition = new Vector2(-200, -100);
        rect.sizeDelta = new Vector2(300, 200);

        // Kill feed entries
        for (int i = 0; i < 5; i++)
        {
            GameObject killEntry = CreateUIText($"Kill Entry {i}", killFeedParent.transform,
                new Vector2(0, -i * 30), "");
            killEntry.GetComponent<TextMeshProUGUI>().fontSize = 14;
            killEntry.GetComponent<TextMeshProUGUI>().alignment = TextAlignmentOptions.Right;
        }
    }

    void CreatePowerUps()
    {
        GameObject powerUpParent = new GameObject("Power-Up System");

        // Damage boost power-ups
        for (int i = 0; i < 3; i++)
        {
            GameObject damageBoost = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            damageBoost.name = $"Damage Boost {i + 1}";
            damageBoost.transform.parent = powerUpParent.transform;
            damageBoost.transform.position = new Vector3(
                Random.Range(-30, 30),
                2,
                Random.Range(-30, 30)
            );
            damageBoost.transform.localScale = Vector3.one * 0.8f;
            damageBoost.GetComponent<Renderer>().material.color = Color.red;
            damageBoost.tag = "PowerUp";

            PowerUp powerUp = damageBoost.AddComponent<PowerUp>();
            powerUp.powerUpType = PowerUpType.DamageBoost;
            powerUp.duration = 15f;
            powerUp.multiplier = 2f;

            // Add floating animation
            damageBoost.AddComponent<FloatingAnimation>();
        }

        // Speed boost power-ups
        for (int i = 0; i < 3; i++)
        {
            GameObject speedBoost = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            speedBoost.name = $"Speed Boost {i + 1}";
            speedBoost.transform.parent = powerUpParent.transform;
            speedBoost.transform.position = new Vector3(
                Random.Range(-30, 30),
                2,
                Random.Range(-30, 30)
            );
            speedBoost.transform.localScale = Vector3.one * 0.8f;
            speedBoost.GetComponent<Renderer>().material.color = Color.blue;
            speedBoost.tag = "PowerUp";

            PowerUp powerUp = speedBoost.AddComponent<PowerUp>();
            powerUp.powerUpType = PowerUpType.SpeedBoost;
            powerUp.duration = 12f;
            powerUp.multiplier = 1.5f;

            speedBoost.AddComponent<FloatingAnimation>();
        }

        // Shield recharge power-ups
        for (int i = 0; i < 2; i++)
        {
            GameObject shieldRecharge = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            shieldRecharge.name = $"Shield Recharge {i + 1}";
            shieldRecharge.transform.parent = powerUpParent.transform;
            shieldRecharge.transform.position = new Vector3(
                Random.Range(-30, 30),
                2,
                Random.Range(-30, 30)
            );
            shieldRecharge.transform.localScale = Vector3.one * 0.8f;
            shieldRecharge.GetComponent<Renderer>().material.color = Color.cyan;
            shieldRecharge.tag = "PowerUp";

            PowerUp powerUp = shieldRecharge.AddComponent<PowerUp>();
            powerUp.powerUpType = PowerUpType.ShieldRecharge;
            powerUp.duration = 0f; // Instant effect
            powerUp.multiplier = 1f;

            shieldRecharge.AddComponent<FloatingAnimation>();
        }
    }

    void CreateGameModes()
    {
        GameObject gameModeManager = new GameObject("Game Mode Manager");
        GameModeManager gmm = gameModeManager.AddComponent<GameModeManager>();

        // Set up different game modes
        gmm.availableGameModes = new GameMode[]
        {
            new GameMode { name = "Team Deathmatch", maxScore = 50, timeLimit = 600 },
            new GameMode { name = "Capture the Flag", maxScore = 3, timeLimit = 900 },
            new GameMode { name = "Control Points", maxScore = 200, timeLimit = 720 },
            new GameMode { name = "Free for All", maxScore = 25, timeLimit = 480 }
        };

        gmm.currentGameMode = gmm.availableGameModes[0]; // Start with Team Deathmatch
    }

    void CreateVisualEffects()
    {
        GameObject vfxParent = new GameObject("Visual Effects System");

        // Muzzle flash effects
        GameObject muzzleFlashPrefab = new GameObject("Muzzle Flash Prefab");
        muzzleFlashPrefab.transform.parent = vfxParent.transform;

        ParticleSystem muzzlePS = muzzleFlashPrefab.AddComponent<ParticleSystem>();
        var main = muzzlePS.main;
        main.startLifetime = 0.1f;
        main.startSpeed = 5f;
        main.startSize = 0.5f;
        main.startColor = Color.yellow;
        main.maxParticles = 50;

        var emission = muzzlePS.emission;
        emission.rateOverTime = 0;
        emission.SetBursts(new ParticleSystem.Burst[] {
            new ParticleSystem.Burst(0.0f, 20)
        });

        // Explosion effects
        GameObject explosionPrefab = new GameObject("Explosion Prefab");
        explosionPrefab.transform.parent = vfxParent.transform;

        ParticleSystem explosionPS = explosionPrefab.AddComponent<ParticleSystem>();
        var expMain = explosionPS.main;
        expMain.startLifetime = 2f;
        expMain.startSpeed = 10f;
        expMain.startSize = 2f;
        expMain.startColor = Color.red;
        expMain.maxParticles = 100;

        // Hit effect
        GameObject hitEffectPrefab = new GameObject("Hit Effect Prefab");
        hitEffectPrefab.transform.parent = vfxParent.transform;

        ParticleSystem hitPS = hitEffectPrefab.AddComponent<ParticleSystem>();
        var hitMain = hitPS.main;
        hitMain.startLifetime = 0.5f;
        hitMain.startSpeed = 3f;
        hitMain.startSize = 0.2f;
        hitMain.startColor = Color.white;
        hitMain.maxParticles = 20;

        // Blood effect
        GameObject bloodEffectPrefab = new GameObject("Blood Effect Prefab");
        bloodEffectPrefab.transform.parent = vfxParent.transform;

        ParticleSystem bloodPS = bloodEffectPrefab.AddComponent<ParticleSystem>();
        var bloodMain = bloodPS.main;
        bloodMain.startLifetime = 1f;
        bloodMain.startSpeed = 2f;
        bloodMain.startSize = 0.1f;
        bloodMain.startColor = Color.red;
        bloodMain.maxParticles = 30;
    }

    void CreateGameSystems()
    {
        // Audio Manager
        GameObject audioManager = new GameObject("Advanced Audio Manager");
        AdvancedAudioManager audioMgr = audioManager.AddComponent<AdvancedAudioManager>();

        // Add multiple audio sources for different sound types
        AudioSource musicSource = audioManager.AddComponent<AudioSource>();
        musicSource.loop = true;
        musicSource.volume = 0.3f;
        musicSource.playOnAwake = false;

        AudioSource sfxSource = audioManager.AddComponent<AudioSource>();
        sfxSource.volume = 0.7f;
        sfxSource.playOnAwake = false;

        AudioSource weaponSource = audioManager.AddComponent<AudioSource>();
        weaponSource.volume = 0.8f;
        weaponSource.playOnAwake = false;

        // Game Manager
        GameObject gameManager = new GameObject("Advanced Game Manager");
        AdvancedGameManager gameManagerComponent = gameManager.AddComponent<AdvancedGameManager>();

        // Network Manager for multiplayer
        GameObject networkManager = new GameObject("Network Manager");
        NetworkManager netMgr = networkManager.AddComponent<NetworkManager>();

        // Statistics Manager
        GameObject statsManager = new GameObject("Statistics Manager");
        StatisticsManager statsMgr = statsManager.AddComponent<StatisticsManager>();

        // Achievement System
        GameObject achievementSystem = new GameObject("Achievement System");
        AchievementManager achievementMgr = achievementSystem.AddComponent<AchievementManager>();

        // Spawn Manager
        GameObject spawnManager = new GameObject("Spawn Manager");
        SpawnManager spawnMgr = spawnManager.AddComponent<SpawnManager>();

        // Objective Manager
        GameObject objectiveManager = new GameObject("Objective Manager");
        ObjectiveManager objMgr = objectiveManager.AddComponent<ObjectiveManager>();

        // Weather System
        GameObject weatherSystem = new GameObject("Weather System");
        WeatherManager weatherMgr = weatherSystem.AddComponent<WeatherManager>();

        // Day/Night Cycle
        GameObject dayNightCycle = new GameObject("Day Night Cycle");
        DayNightCycle dayNight = dayNightCycle.AddComponent<DayNightCycle>();

        // Performance Manager
        GameObject performanceManager = new GameObject("Performance Manager");
        PerformanceManager perfMgr = performanceManager.AddComponent<PerformanceManager>();

        Debug.Log("🎮 All advanced game systems created!");
    }

    // Utility methods for UI creation
    GameObject CreateUISlider(string name, Transform parent, Vector2 position, Vector2 size)
    {
        GameObject slider = new GameObject(name);
        slider.transform.SetParent(parent, false);

        RectTransform rect = slider.AddComponent<RectTransform>();
        rect.anchoredPosition = position;
        rect.sizeDelta = size;

        Slider sliderComponent = slider.AddComponent<Slider>();

        // Background
        GameObject background = new GameObject("Background");
        background.transform.SetParent(slider.transform, false);
        Image bgImage = background.AddComponent<Image>();
        bgImage.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        RectTransform bgRect = background.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.offsetMin = Vector2.zero;
        bgRect.offsetMax = Vector2.zero;

        // Fill Area
        GameObject fillArea = new GameObject("Fill Area");
        fillArea.transform.SetParent(slider.transform, false);
        RectTransform fillAreaRect = fillArea.GetComponent<RectTransform>();
        fillAreaRect.anchorMin = Vector2.zero;
        fillAreaRect.anchorMax = Vector2.one;
        fillAreaRect.offsetMin = Vector2.zero;
        fillAreaRect.offsetMax = Vector2.zero;

        // Fill
        GameObject fill = new GameObject("Fill");
        fill.transform.SetParent(fillArea.transform, false);
        Image fillImage = fill.AddComponent<Image>();
        fillImage.color = Color.green;
        RectTransform fillRect = fill.GetComponent<RectTransform>();
        fillRect.anchorMin = Vector2.zero;
        fillRect.anchorMax = Vector2.one;
        fillRect.offsetMin = Vector2.zero;
        fillRect.offsetMax = Vector2.zero;

        sliderComponent.fillRect = fillRect;
        sliderComponent.value = 1f;

        return slider;
    }

    GameObject CreateUIText(string name, Transform parent, Vector2 position, string text)
    {
        GameObject textObj = new GameObject(name);
        textObj.transform.SetParent(parent, false);

        RectTransform rect = textObj.AddComponent<RectTransform>();
        rect.anchoredPosition = position;
        rect.sizeDelta = new Vector2(200, 30);

        TextMeshProUGUI textComponent = textObj.AddComponent<TextMeshProUGUI>();
        textComponent.text = text;
        textComponent.fontSize = 18;
        textComponent.color = Color.white;
        textComponent.alignment = TextAlignmentOptions.Center;

        return textObj;
    }

    GameObject CreateUIImage(string name, Transform parent, Vector2 position, Vector2 size)
    {
        GameObject imageObj = new GameObject(name);
        imageObj.transform.SetParent(parent, false);

        RectTransform rect = imageObj.AddComponent<RectTransform>();
        rect.anchoredPosition = position;
        rect.sizeDelta = size;

        Image image = imageObj.AddComponent<Image>();
        image.color = Color.white;

        return imageObj;
    }
}

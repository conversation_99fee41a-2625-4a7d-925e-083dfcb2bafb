using UnityEngine;

public class AdvancedPlayerController : MonoBehaviour
{
    [Header("Movement Settings")]
    public float walkSpeed = 6f;
    public float sprintSpeed = 12f;
    public float crouchSpeed = 3f;
    public float jumpHeight = 3f;
    public float gravity = -20f;
    public float airControl = 0.3f;
    
    [Header("Advanced Movement")]
    public float slideSpeed = 15f;
    public float slideDuration = 1f;
    public float wallRunSpeed = 8f;
    public float wallRunDuration = 3f;
    public float doubleJumpHeight = 2f;
    
    [Header("Camera Settings")]
    public float mouseSensitivity = 2f;
    public float maxLookAngle = 80f;
    public Camera playerCamera;
    
    [Header("Ground Check")]
    public Transform groundCheck;
    public float groundDistance = 0.4f;
    public LayerMask groundMask = 1;
    
    private CharacterController controller;
    private Vector3 velocity;
    private bool isGrounded;
    private bool isSprinting;
    private bool isCrouching;
    private bool isSliding;
    private bool isWallRunning;
    private bool canDoubleJump;
    private float slideTimer;
    private float wallRunTimer;
    private float xRotation = 0f;
    
    // Input
    private float horizontal;
    private float vertical;
    private bool jumpPressed;
    private bool sprintPressed;
    private bool crouchPressed;
    
    void Start()
    {
        controller = GetComponent<CharacterController>();
        if (playerCamera == null)
            playerCamera = GetComponentInChildren<Camera>();
        
        Cursor.lockState = CursorLockMode.Locked;
    }
    
    void Update()
    {
        HandleInput();
        HandleMouseLook();
        HandleMovement();
        HandleAdvancedMovement();
    }
    
    void HandleInput()
    {
        horizontal = Input.GetAxis("Horizontal");
        vertical = Input.GetAxis("Vertical");
        jumpPressed = Input.GetButtonDown("Jump");
        sprintPressed = Input.GetKey(KeyCode.LeftShift);
        crouchPressed = Input.GetKey(KeyCode.LeftControl);
        
        // Weapon switching
        if (Input.GetKeyDown(KeyCode.Alpha1)) SwitchWeapon(0);
        if (Input.GetKeyDown(KeyCode.Alpha2)) SwitchWeapon(1);
        if (Input.GetKeyDown(KeyCode.Alpha3)) SwitchWeapon(2);
        if (Input.GetKeyDown(KeyCode.Alpha4)) SwitchWeapon(3);
        
        // Abilities
        if (Input.GetKeyDown(KeyCode.Q)) UseAbility(0);
        if (Input.GetKeyDown(KeyCode.E)) UseAbility(1);
        if (Input.GetKeyDown(KeyCode.F)) UseAbility(2);
    }
    
    void HandleMouseLook()
    {
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
        
        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, -maxLookAngle, maxLookAngle);
        
        playerCamera.transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
        transform.Rotate(Vector3.up * mouseX);
    }
    
    void HandleMovement()
    {
        isGrounded = Physics.CheckSphere(groundCheck.position, groundDistance, groundMask);
        
        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;
            canDoubleJump = true;
        }
        
        // Determine movement speed
        float currentSpeed = walkSpeed;
        if (isSprinting && !isCrouching) currentSpeed = sprintSpeed;
        if (isCrouching && !isSliding) currentSpeed = crouchSpeed;
        if (isSliding) currentSpeed = slideSpeed;
        
        Vector3 move = transform.right * horizontal + transform.forward * vertical;
        
        if (!isGrounded)
            move *= airControl;
        
        controller.Move(move * currentSpeed * Time.deltaTime);
        
        // Jumping
        if (jumpPressed)
        {
            if (isGrounded)
            {
                velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
            }
            else if (canDoubleJump)
            {
                velocity.y = Mathf.Sqrt(doubleJumpHeight * -2f * gravity);
                canDoubleJump = false;
            }
        }
        
        velocity.y += gravity * Time.deltaTime;
        controller.Move(velocity * Time.deltaTime);
    }
    
    void HandleAdvancedMovement()
    {
        // Sliding
        if (crouchPressed && isSprinting && isGrounded && !isSliding)
        {
            StartSlide();
        }
        
        if (isSliding)
        {
            slideTimer -= Time.deltaTime;
            if (slideTimer <= 0 || !crouchPressed)
            {
                StopSlide();
            }
        }
        
        // Wall running (simplified)
        if (!isGrounded && !isWallRunning)
        {
            CheckForWallRun();
        }
        
        if (isWallRunning)
        {
            wallRunTimer -= Time.deltaTime;
            if (wallRunTimer <= 0 || isGrounded)
            {
                StopWallRun();
            }
        }
        
        // Update states
        isSprinting = sprintPressed && !isCrouching;
        isCrouching = crouchPressed && !isSliding;
    }
    
    void StartSlide()
    {
        isSliding = true;
        slideTimer = slideDuration;
        controller.height = 1f; // Crouch height
    }
    
    void StopSlide()
    {
        isSliding = false;
        controller.height = 2f; // Normal height
    }
    
    void CheckForWallRun()
    {
        // Simplified wall run detection
        RaycastHit hit;
        if (Physics.Raycast(transform.position, transform.right, out hit, 1f) ||
            Physics.Raycast(transform.position, -transform.right, out hit, 1f))
        {
            if (hit.collider.CompareTag("Wall"))
            {
                StartWallRun();
            }
        }
    }
    
    void StartWallRun()
    {
        isWallRunning = true;
        wallRunTimer = wallRunDuration;
        velocity.y = 0; // Stop falling
    }
    
    void StopWallRun()
    {
        isWallRunning = false;
    }
    
    void SwitchWeapon(int weaponIndex)
    {
        WeaponManager weaponManager = GetComponentInChildren<WeaponManager>();
        if (weaponManager != null)
        {
            weaponManager.SwitchWeapon(weaponIndex);
        }
    }
    
    void UseAbility(int abilityIndex)
    {
        PlayerAbilities abilities = GetComponent<PlayerAbilities>();
        if (abilities != null)
        {
            abilities.UseAbility(abilityIndex);
        }
    }
}

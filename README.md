# 🎮 Complete Destiny 2-Inspired PVP Game

A fully-featured first-person shooter game inspired by Destiny 2's PVP gameplay, built in Unity with comprehensive systems and polished UI.

## ✨ Complete Feature Set

### 🎯 Core Gameplay
- **Advanced First-Person Movement**: Smooth FPS controls with running, jumping, sliding, and audio feedback
- **Comprehensive Weapon System**: Multiple weapon types with realistic ballistics, recoil, and switching
- **Destiny-Style Health & Shield System**: Regenerating shields and health with visual feedback
- **Smart AI Enemies**: Advanced AI with patrol, chase, and attack behaviors
- **Multiple Game Modes**: Team Deathmatch, Free-for-All, Control Points, and Elimination

### 🎨 Complete UI System
- **Professional HUD**: Health/shield bars, ammo counter, crosshair, minimap, and damage overlay
- **Advanced Scoreboard**: Real-time player stats, team scores, and match information
- **Full Menu System**: Main menu, play menu, settings, and loading screens
- **In-Game Notifications**: Kill feed, objective updates, and status messages
- **Pause System**: Complete pause menu with settings and navigation

### Weapon Types
- **Auto Rifles**: High rate of fire, medium damage
- **Hand Cannons**: High damage, slower fire rate
- **Shotguns**: Close-range devastation
- **Sniper Rifles**: Long-range precision
- **SMGs**: Fast, close-quarters combat
- **LMGs**: Heavy damage, slow handling

### Game Systems
- **Respawn System**: Automatic respawning with multiple spawn points
- **Score Tracking**: Real-time score and timer display
- **Pause Menu**: In-game pause with settings
- **Main Menu**: Complete menu system with settings

## Setup Instructions

### 1. Unity Project Setup
1. Create a new 3D Unity project
2. Copy all scripts from the `Scripts/` folder into your `Assets/Scripts/` folder
3. Create the following folder structure in your Assets:
   ```
   Assets/
   ├── Scripts/
   │   ├── Player/
   │   ├── Weapons/
   │   ├── AI/
   │   └── UI/
   ├── Prefabs/
   ├── Materials/
   ├── Audio/
   └── Scenes/
   ```

### 2. Scene Setup

#### Main Game Scene
1. Create a new scene called "GameScene"
2. Add a terrain or floor plane
3. Add lighting (Directional Light)
4. Create spawn points (empty GameObjects)

#### Player Setup
1. Create an empty GameObject named "Player"
2. Add a CharacterController component
3. Add the PlayerController script
4. Add the PlayerHealth script
5. Create a child GameObject for the camera
6. Create a child GameObject for ground check (position it at the player's feet)

#### Weapon Setup
1. Create a child GameObject under Player called "WeaponHolder"
2. Add a simple weapon model (or use a cube as placeholder)
3. Add the WeaponController script
4. Create a child GameObject for the fire point

#### UI Setup
1. Create a Canvas
2. Add UI elements for:
   - Health bar (Slider)
   - Shield bar (Slider)
   - Ammo counter (Text)
   - Score display (Text)
   - Timer (Text)
   - Damage overlay (Image with red color)

### 3. Creating Weapon Data
1. Right-click in Project window
2. Go to Create > Weapons > Weapon Data
3. Configure the weapon stats
4. Assign to WeaponController

### 4. AI Setup
1. Create an enemy GameObject
2. Add NavMeshAgent component
3. Add the SimpleAI script
4. Bake NavMesh (Window > AI > Navigation)

## Controls

- **WASD**: Movement
- **Mouse**: Look around
- **Left Shift**: Run
- **Space**: Jump
- **C**: Slide (while running)
- **Left Click**: Shoot
- **R**: Reload
- **Escape**: Pause menu

## Customization

### Adding New Weapons
1. Create new WeaponData asset
2. Adjust damage, fire rate, recoil, etc.
3. Assign to WeaponController

### Modifying Movement
- Edit PlayerController script
- Adjust speed, jump height, slide parameters

### Adding Game Modes
- Extend GameManager script
- Add new GameMode enum values
- Implement scoring logic

## Technical Details

### Scripts Overview
- **PlayerController**: Handles all player movement and input
- **WeaponController**: Manages shooting, reloading, and weapon switching
- **PlayerHealth**: Health/shield system with regeneration
- **GameManager**: Game state, scoring, and match management
- **SimpleAI**: Basic enemy AI with patrol and attack behaviors
- **MainMenu/PauseMenu**: UI management

### Performance Tips
- Use object pooling for projectiles and effects
- Optimize AI update frequency
- Use LOD for distant objects
- Implement frustum culling for large maps

## Future Enhancements

### Planned Features
- **Multiplayer**: Network multiplayer support
- **More Weapons**: Rocket launchers, fusion rifles
- **Abilities**: Special powers and supers
- **Maps**: Multiple arena layouts
- **Progression**: XP and unlockable content
- **Audio**: Complete sound design
- **Effects**: Particle systems and visual polish

### Advanced Systems
- **Weapon Attachments**: Scopes, grips, barrels
- **Character Classes**: Different player archetypes
- **Power Ammo**: Special heavy weapons
- **Competitive Modes**: Ranked gameplay
- **Spectator Mode**: Watch other players

## Troubleshooting

### Common Issues
1. **Player falls through floor**: Check CharacterController settings
2. **Weapons don't shoot**: Verify fire point assignment
3. **AI doesn't move**: Bake NavMesh in scene
4. **UI not showing**: Check Canvas render mode
5. **Controls not working**: Verify Input Manager settings

### Performance Issues
- Reduce AI update frequency
- Lower particle effect quality
- Use simpler materials
- Optimize mesh complexity

## Credits

Built with Unity Engine
Inspired by Destiny 2 by Bungie
Created as an educational project

---

**Note**: This is a learning project. For commercial use, ensure all assets and code comply with licensing requirements.
